const { getS3Instance } = require('./s3Utils');

/**
 * Validate if a preview URL is accessible
 * @param {string} previewUrl - The preview URL to validate
 * @returns {Promise<boolean>} - True if accessible, false otherwise
 */
const validatePreviewUrl = async (previewUrl) => {
  try {
    if (!previewUrl) {
      return false;
    }

    // Check if it's an S3 URL
    if (previewUrl.includes('amazonaws.com') || previewUrl.includes('s3.')) {
      return await validateS3PreviewUrl(previewUrl);
    } else {
      // For local files, check if file exists
      const fs = require('fs');
      const path = require('path');
      
      // Convert URL to local path
      const localPath = previewUrl.startsWith('/uploads') 
        ? path.join('.', previewUrl)
        : previewUrl;
      
      return fs.existsSync(localPath);
    }
  } catch (error) {
    console.error('Error validating preview URL:', error);
    return false;
  }
};

/**
 * Validate if an S3 preview URL is accessible
 * @param {string} s3Url - The S3 URL to validate
 * @returns {Promise<boolean>} - True if accessible, false otherwise
 */
const validateS3PreviewUrl = async (s3Url) => {
  try {
    const s3 = getS3Instance();
    const bucketName = process.env.AWS_BUCKET_NAME;

    // Extract S3 key from URL
    let s3Key;
    if (s3Url.includes('amazonaws.com')) {
      const urlParts = s3Url.split('/');
      const bucketIndex = urlParts.findIndex(part => part.includes(bucketName));
      if (bucketIndex !== -1) {
        s3Key = urlParts.slice(bucketIndex + 1).join('/');
      } else {
        const domainIndex = urlParts.findIndex(part => part.includes('amazonaws.com'));
        s3Key = urlParts.slice(domainIndex + 1).join('/');
      }
    } else {
      throw new Error('Invalid S3 URL format');
    }

    // Check if object exists
    const params = {
      Bucket: bucketName,
      Key: s3Key
    };

    await s3.headObject(params).promise();
    return true;
  } catch (error) {
    if (error.code === 'NotFound' || error.code === 'NoSuchKey') {
      console.log(`[PreviewValidator] S3 object not found: ${s3Url}`);
      return false;
    }
    console.error('Error validating S3 preview URL:', error);
    return false;
  }
};

/**
 * Update content preview status based on URL validation
 * @param {string} contentId - The content ID
 * @param {string} previewUrl - The preview URL to validate
 * @returns {Promise<Object>} - Updated content data
 */
const updatePreviewStatusAfterValidation = async (contentId, previewUrl) => {
  try {
    const Content = require('../models/Content');
    const isValid = await validatePreviewUrl(previewUrl);
    
    const updateData = {
      previewStatus: isValid ? 'completed' : 'failed',
      previewError: isValid ? null : 'Preview file not accessible'
    };

    const updatedContent = await Content.findByIdAndUpdate(
      contentId,
      updateData,
      { new: true }
    );

    console.log(`[PreviewValidator] Updated content ${contentId} preview status to: ${updateData.previewStatus}`);
    return updatedContent;
  } catch (error) {
    console.error('Error updating preview status after validation:', error);
    throw error;
  }
};

module.exports = {
  validatePreviewUrl,
  validateS3PreviewUrl,
  updatePreviewStatusAfterValidation
};
