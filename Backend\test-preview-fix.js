/**
 * Test script to verify preview URL database fix
 * This script tests the preview generation and validation functionality
 */

const { validatePreviewUrl, validateS3PreviewUrl } = require('./utils/previewValidator');

async function testPreviewValidation() {
  console.log('=== Testing Preview URL Validation ===\n');

  // Test cases
  const testUrls = [
    {
      name: 'Valid S3 URL (should exist)',
      url: 'https://xosports.s3.amazonaws.com/previews/test_preview.mp4',
      expected: false // Assuming this doesn't exist
    },
    {
      name: 'Invalid S3 URL format',
      url: 'https://invalid-url.com/test.mp4',
      expected: false
    },
    {
      name: 'Local file (non-existent)',
      url: '/uploads/previews/non-existent.mp4',
      expected: false
    },
    {
      name: 'Null URL',
      url: null,
      expected: false
    },
    {
      name: 'Empty URL',
      url: '',
      expected: false
    }
  ];

  for (const testCase of testUrls) {
    try {
      console.log(`Testing: ${testCase.name}`);
      console.log(`URL: ${testCase.url}`);
      
      const result = await validatePreviewUrl(testCase.url);
      const status = result === testCase.expected ? '✅ PASS' : '❌ FAIL';
      
      console.log(`Expected: ${testCase.expected}, Got: ${result} ${status}`);
      console.log('---');
    } catch (error) {
      console.error(`Error testing ${testCase.name}:`, error.message);
      console.log('---');
    }
  }
}

async function testS3PreviewValidation() {
  console.log('\n=== Testing S3 Preview URL Validation ===\n');

  // Test with a real S3 URL from your bucket
  const s3TestUrl = 'https://xosports.s3.amazonaws.com/previews/test_chunk_preview.mp4';
  
  try {
    console.log(`Testing S3 URL: ${s3TestUrl}`);
    const result = await validateS3PreviewUrl(s3TestUrl);
    console.log(`Result: ${result ? 'EXISTS' : 'NOT FOUND'}`);
  } catch (error) {
    console.error('Error testing S3 URL:', error.message);
  }
}

// Run tests
async function runTests() {
  try {
    await testPreviewValidation();
    await testS3PreviewValidation();
    
    console.log('\n=== Test Summary ===');
    console.log('✅ Preview validation tests completed');
    console.log('📝 Check the results above to ensure validation is working correctly');
    console.log('\n💡 To test with real data:');
    console.log('1. Upload a video file using chunked upload');
    console.log('2. Check the preview URL in the database');
    console.log('3. Use the /api/content/:id/preview-status endpoint to validate');
    
  } catch (error) {
    console.error('Test execution error:', error);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  testPreviewValidation,
  testS3PreviewValidation,
  runTests
};
