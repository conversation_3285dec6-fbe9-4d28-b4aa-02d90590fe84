const ErrorResponse = require("../utils/errorResponse");
const Content = require("../models/Content");
const User = require("../models/User");
const { validationResult } = require("express-validator");
const { cleanupPreviewFile, ensurePreviewDirectories } = require("../utils/previewGenerator");
const { getContentFileAccess } = require("../utils/accessControl");
const sendAdminNotification = require('../utils/sendAdminNotification');
const { isDateInFutureUTC, compareUTCDates } = require("../utils/dateUtils");

// Ensure preview directories exist on module load
ensurePreviewDirectories();

// @desc    Get all content
// @route   GET /api/content
// @access  Public
exports.getAllContent = async (req, res, next) => {
  try {
    let query;

    // Copy req.query
    const reqQuery = { ...req.query };

    // Fields to exclude
    const removeFields = [
      "page",
      "limit",
      "sport",
      "priceMin",
      "priceMax",
      "sortBy",
      "search",
      "contentType",
      "difficulty",
      "saleType",
    ];

    // Loop over removeFields and delete them from reqQuery
    removeFields.forEach((param) => delete reqQuery[param]);

    // Create query string
    let queryStr = JSON.stringify(reqQuery);

    // Create operators ($gt, $gte, etc)
    queryStr = queryStr.replace(
      /\b(gt|gte|lt|lte|in)\b/g,
      (match) => `$${match}`
    );

    // Parse the query string
    let queryObj = JSON.parse(queryStr);

    // Base query - show both Published and Draft content
    const baseQuery = {
      status: "Published",
      visibility: "Public",
      isActive: { $in: [1, true] },
      // Exclude sold content and ended auctions from public listing
      $and: [
        {
          $or: [
            { isSold: { $ne: true } },
            { isSold: { $exists: false } }
          ]
        },
        {
          $or: [
            // Content is not auction type
            { saleType: "Fixed" },
            // Auction content that hasn't ended yet
            {
              $and: [
                { saleType: { $in: ["Auction", "Both"] } },
                {
                  $or: [
                    { auctionStatus: { $ne: "Ended" } },
                    { auctionStatus: { $exists: false } }
                  ]
                }
              ]
            }
          ]
        }
      ],
      ...queryObj,
    };

    // Handle sport filter
    if (req.query.sport) {
      baseQuery.sport = req.query.sport;
    }

    // Handle content type filter
    if (req.query.contentType) {
      const contentTypes = Array.isArray(req.query.contentType)
        ? req.query.contentType
        : [req.query.contentType];
      if (contentTypes.length > 0) {
        baseQuery.contentType = { $in: contentTypes };
      }
    }
    // Handle difficulty filter
    if (req.query.difficulty) {
      const difficulties = Array.isArray(req.query.difficulty)
        ? req.query.difficulty
        : [req.query.difficulty];
      if (difficulties.length > 0) {
        baseQuery.difficulty = { $in: difficulties };
      }
    }
    // Handle sale type filter
    if (req.query.saleType) {
      const saleTypes = Array.isArray(req.query.saleType)
        ? req.query.saleType
        : [req.query.saleType];
      if (saleTypes.length > 0) {
        baseQuery.saleType = { $in: saleTypes };
      }
    }

    // Handle price range - considering both price and auction basePrice
    if (req.query.priceMin || req.query.priceMax) {
      // Create aggregation pipeline stage to calculate effective price
      const priceMin = req.query.priceMin ? parseFloat(req.query.priceMin) : 0;
      const priceMax = req.query.priceMax ? parseFloat(req.query.priceMax) : Number.MAX_SAFE_INTEGER;

      // Add a condition that uses the same logic as the categories endpoint
      baseQuery.$expr = {
        $and: [
          {
            $gte: [
              {
                $cond: {
                  if: { $and: [{ $ne: ["$saleType", "Fixed"] }, { $gt: ["$auctionDetails.basePrice", 0] }] },
                  then: "$auctionDetails.basePrice",
                  else: { $ifNull: ["$price", 0] }
                }
              },
              priceMin
            ]
          },
          {
            $lte: [
              {
                $cond: {
                  if: { $and: [{ $ne: ["$saleType", "Fixed"] }, { $gt: ["$auctionDetails.basePrice", 0] }] },
                  then: "$auctionDetails.basePrice",
                  else: { $ifNull: ["$price", 0] }
                }
              },
              priceMax
            ]
          }
        ]
      };
    }

    // Handle search
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, "i");
      baseQuery.$or = [
        { title: searchRegex },
        { description: searchRegex },
        { tags: searchRegex },
      ];
    }

    // Count total before pagination
    const total = await Content.countDocuments(baseQuery);

    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = 9; // Fixed limit of 9 items per page
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;

    // Handle sorting - use aggregation pipeline for price sorting to support both fixed and auction content
    let content;
    if (req.query.sortBy && (req.query.sortBy === "price_asc" || req.query.sortBy === "price_desc")) {
      // Use aggregation pipeline for price sorting to handle both price and basePrice
      const sortDirection = req.query.sortBy === "price_asc" ? 1 : -1;
      
      const aggregationPipeline = [
        { $match: baseQuery },
        {
          $addFields: {
            effectivePrice: {
              $cond: {
                if: { 
                  $and: [
                    { $ne: ["$saleType", "Fixed"] }, 
                    { $gt: ["$auctionDetails.basePrice", 0] }
                  ]
                },
                then: "$auctionDetails.basePrice",
                else: { $ifNull: ["$price", 0] }
              }
            }
          }
        },
        { $sort: { effectivePrice: sortDirection } },
        { $skip: startIndex },
        { $limit: limit },
        {
          $lookup: {
            from: "users",
            localField: "seller",
            foreignField: "_id",
            as: "seller",
            pipeline: [
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  profileImage: 1,
                  isVerified: 1
                }
              }
            ]
          }
        },
        {
          $unwind: {
            path: "$seller",
            preserveNullAndEmptyArrays: true
          }
        }
      ];

      content = await Content.aggregate(aggregationPipeline);
    } else {
      // Use regular query for non-price sorting
      const sortMap = {
        newest: "-createdAt",
        oldest: "createdAt",
        rating: "-averageRating",
      };
      const sortField = sortMap[req.query.sortBy] || "-createdAt";

      const query = Content.find(baseQuery)
        .sort(sortField)
        .skip(startIndex)
        .limit(limit)
        .populate({
          path: "seller",
          select: "firstName lastName profileImage isVerified",
        });

      content = await query;
    }

    // Pagination result
    const pagination = {};

    if (endIndex < total) {
      pagination.next = {
        page: page + 1,
        limit,
      };
    }

    if (startIndex > 0) {
      pagination.prev = {
        page: page - 1,
        limit,
      };
    }

    res.status(200).json({
      success: true,
      count: content.length,
      total,
      pagination,
      data: content,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single content
// @route   GET /api/content/:id
// @access  Public
exports.getContent = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id).populate({
      path: "seller",
      select: "firstName lastName profileImage isVerified",
    });

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Check if content is published or user is the seller or admin
    if (
      content.status !== "Published" &&
      (!req.user ||
        (req.user.id !== content.seller._id.toString() &&
          req.user.role !== "admin" &&
          req.user.role !== "buyer"))
    ) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Get appropriate file access based on user's purchase status
    const userId = req.user ? req.user.id : null;
    const userRole = req.user ? req.user.role : null;

    const fileAccess = await getContentFileAccess(content, userId, userRole);

    // Create response data with access-controlled file information
    const responseData = {
      ...content.toObject(),
      fileAccess: {
        accessType: fileAccess.accessType,
        canAccessFull: fileAccess.canAccessFull,
        hasPreview: fileAccess.hasPreview,
        reason: fileAccess.reason
      }
    };

    // Control access to file URLs based on user's purchase status
    if (fileAccess.accessType === 'full') {
      // User has full access - show both original and preview URLs
      responseData.accessibleFileUrl = content.fileUrl;
      responseData.fileUrl = content.fileUrl;
      responseData.previewUrl = content.previewUrl;
    } else if (fileAccess.accessType === 'preview' && content.previewUrl) {
      // User has preview access only - hide original file URL
      responseData.accessibleFileUrl = content.previewUrl;
      responseData.previewUrl = content.previewUrl;
      delete responseData.fileUrl; // Remove access to original file URL
    } else {
      // No access - hide all file URLs
      responseData.accessibleFileUrl = null;
      delete responseData.fileUrl;
      delete responseData.previewUrl;
    }

    res.status(200).json({
      success: true,
      data: responseData,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create content
// @route   POST /api/content
// @access  Private/Seller
exports.createContent = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    // Add user to req.body
    req.body.seller = req.user.id;

    // Validate auction dates if auction type is selected
    if (req.body.saleType === "Auction" && req.body.auctionDetails) {
      // Validate auction start date
      if (req.body.auctionDetails.auctionStartDate) {
        if (!isDateInFutureUTC(req.body.auctionDetails.auctionStartDate)) {
          return next(
            new ErrorResponse(
              "Auction start date must be in the future",
              400
            )
          );
        }
      }

      // Validate auction end date
      if (req.body.auctionDetails.auctionEndDate && req.body.auctionDetails.auctionStartDate) {
        if (compareUTCDates(req.body.auctionDetails.auctionEndDate, req.body.auctionDetails.auctionStartDate) <= 0) {
          return next(
            new ErrorResponse(
              "Auction end date must be after start date",
              400
            )
          );
        }
      }
    }

    // Check if user has seller access (using effective role like the auth middleware)
    const user = await User.findById(req.user.id);
    const effectiveRole = user.role === 'admin' ? user.role : user.activeRole;

    if (effectiveRole !== "seller" && effectiveRole !== "admin") {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to create content`,
          403
        )
      );
    }

    // Check if seller has completed Stripe Connect onboarding (for non-admin users)
    if (effectiveRole === "seller") {
      if (!user.paymentInfo?.stripeConnectId) {
        return next(
          new ErrorResponse(
            `Payment setup required. Please complete your Stripe Connect onboarding before creating content.`,
            400
          )
        );
      }

      // Verify Stripe Connect account exists and is properly set up
      try {
        const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
        const connectAccount = await stripe.accounts.retrieve(user.paymentInfo.stripeConnectId);

        if (!connectAccount.details_submitted || !connectAccount.charges_enabled) {
          return next(
            new ErrorResponse(
              `Payment setup incomplete. Please complete your Stripe Connect onboarding to start creating content.`,
              400
            )
          );
        }
      } catch (stripeError) {
        console.error('Stripe Connect verification error:', stripeError);
        return next(
          new ErrorResponse(
            `Invalid payment setup. Please complete your Stripe Connect onboarding again.`,
            400
          )
        );
      }
    }

    // Set initial preview status
    req.body.previewStatus = 'pending';

    // Create content first without waiting for preview generation
    const content = await Content.create(req.body);

    // Note: Preview generation is now handled during chunked upload for videos
    // For other content types, preview can be generated later if needed
    if (req.body.contentType === 'Video') {
      console.log(`[Content] Video content created. Preview should have been generated during chunked upload.`);
    }

    // Send email notification to all admin users (don't await - run in background)
    const seller = await User.findById(req.user.id).select('firstName lastName email');
    sendAdminNotification({
      subject: 'New Content Submitted for Review - XO Sports Hub',
      message: `A new content has been submitted by ${seller.firstName} ${seller.lastName} and is pending review.\n\nContent Title: ${content.title}\nSport: ${content.sport}\nContent Type: ${content.contentType}\nSeller: ${seller.firstName} ${seller.lastName} (${seller.email})\n\nPlease log in to the admin panel to review and approve this content.`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">New Content Submitted for Review</h2>

          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #007bff; margin-top: 0;">Content Details</h3>
            <p><strong>Title:</strong> ${content.title}</p>
            <p><strong>Sport:</strong> ${content.sport}</p>
            <p><strong>Content Type:</strong> ${content.contentType}</p>
            <p><strong>Price:</strong> $${content.price}</p>
            <p><strong>Status:</strong> ${content.status}</p>
          </div>

          <div style="background-color: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #28a745; margin-top: 0;">Seller Information</h3>
            <p><strong>Name:</strong> ${seller.firstName} ${seller.lastName}</p>
            <p><strong>Email:</strong> ${seller.email}</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <p style="color: #666;">Please log in to the admin panel to review and approve this content.</p>
            <a href="${process.env.FRONTEND_URL}/admin/content"
               style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Review Content
            </a>
          </div>

          <hr style="border: none; border-top: 1px solid #dee2e6; margin: 30px 0;">
          <p style="color: #6c757d; font-size: 12px; text-align: center;">
            This is an automated notification from XO Sports Hub Admin System.
          </p>
        </div>
      `
    }).catch(error => {
      console.error(`[Content] Failed to send admin notification for content ${content._id}:`, error);
    });

    // Return response immediately
    res.status(201).json({
      success: true,
      data: content,
      message: req.body.contentType === 'Video'
        ? 'Content created successfully. Preview should be available from chunked upload.'
        : 'Content created successfully.'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get preview generation status
// @route   GET /api/content/:id/preview-status
// @access  Public
exports.getPreviewStatus = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id).select('previewStatus previewError previewUrl');

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // If preview URL exists but status is still pending, validate the URL
    if (content.previewUrl && content.previewStatus === 'pending') {
      try {
        const { validatePreviewUrl } = require('../utils/previewValidator');
        const isValid = await validatePreviewUrl(content.previewUrl);

        // Update status based on validation
        const newStatus = isValid ? 'completed' : 'failed';
        const updateData = {
          previewStatus: newStatus,
          previewError: isValid ? null : 'Preview file not accessible'
        };

        await Content.findByIdAndUpdate(req.params.id, updateData);

        return res.status(200).json({
          success: true,
          data: {
            previewStatus: newStatus,
            previewError: updateData.previewError,
            hasPreview: isValid
          }
        });
      } catch (validationError) {
        console.error('Error validating preview URL:', validationError);
        // Continue with original status if validation fails
      }
    }

    res.status(200).json({
      success: true,
      data: {
        previewStatus: content.previewStatus,
        previewError: content.previewError,
        hasPreview: !!content.previewUrl
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Validate and update preview status
// @route   POST /api/content/:id/validate-preview
// @access  Private/Admin
exports.validatePreviewStatus = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id);

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    if (!content.previewUrl) {
      return res.status(400).json({
        success: false,
        message: 'No preview URL to validate'
      });
    }

    const { updatePreviewStatusAfterValidation } = require('../utils/previewValidator');
    const updatedContent = await updatePreviewStatusAfterValidation(req.params.id, content.previewUrl);

    res.status(200).json({
      success: true,
      data: {
        previewStatus: updatedContent.previewStatus,
        previewError: updatedContent.previewError,
        hasPreview: !!updatedContent.previewUrl
      }
    });
  } catch (err) {
    next(err);
  }
};

// Note: Background preview generation removed - now handled during chunked upload for videos

// @desc    Update content
// @route   PUT /api/content/:id
// @access  Private/Seller
exports.updateContent = async (req, res, next) => {
  try {
    let content = await Content.findById(req.params.id);

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is content seller
    if (
      content.seller.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to update this content`,
          403
        )
      );
    }

    // Validate auction dates if auction type is selected
    if (req.body.saleType === "Auction" && req.body.auctionDetails) {
      // Validate auction start date
      if (req.body.auctionDetails.auctionStartDate) {
        if (!isDateInFutureUTC(req.body.auctionDetails.auctionStartDate)) {
          return next(
            new ErrorResponse(
              "Auction start date must be in the future",
              400
            )
          );
        }
      }

      // Validate auction end date
      if (req.body.auctionDetails.auctionEndDate && req.body.auctionDetails.auctionStartDate) {
        if (compareUTCDates(req.body.auctionDetails.auctionEndDate, req.body.auctionDetails.auctionStartDate) <= 0) {
          return next(
            new ErrorResponse(
              "Auction end date must be after start date",
              400
            )
          );
        }
      }
    }

    // Check if file URL is being updated and regenerate preview if needed
    let previewUrl = content.previewUrl; // Keep existing preview by default

    if (req.body.fileUrl && req.body.fileUrl !== content.fileUrl) {
      console.log(`[Update] File URL changed, regenerating preview for content: ${content.title}`);

      try {
        // Extract filename from new fileUrl
        const fileName = req.body.fileUrl.split('/').pop();
        const contentType = req.body.contentType || content.contentType;

        // For video files, preview should come from chunked upload
        if (contentType === 'Video') {
          console.log(`[Update] Video file updated. Preview should be provided from chunked upload.`);
          // Keep existing preview URL unless a new one is explicitly provided
          if (!req.body.previewUrl) {
            req.body.previewUrl = content.previewUrl;
          }
        } else {
          // For non-video files, clear preview as we're not generating them anymore
          console.log(`[Update] Non-video file updated. Preview generation disabled.`);
          req.body.previewUrl = null;
        }
      } catch (error) {
        console.error('[Update] Error handling file update:', error);
        // Keep existing preview on error
        req.body.previewUrl = content.previewUrl;
      }
    }

    content = await Content.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    res.status(200).json({
      success: true,
      data: content,
      message: req.body.previewUrl && req.body.previewUrl !== previewUrl ?
        'Content updated with new preview' : 'Content updated successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete content
// @route   DELETE /api/content/:id
// @access  Private/Seller
exports.deleteContent = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id);

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Authorization check
    if (
      content.seller.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to delete this content`,
          403
        )
      );
    }

    // Clean up preview file if it exists
    if (content.previewUrl) {
      try {
        console.log(`[Delete] Cleaning up preview file for content: ${content.title}`);

        // Determine if file is on S3 or local using the storage helper
        const isS3Upload = isS3Url(content.previewUrl);

        await cleanupPreviewFile(content.previewUrl, isS3Upload);
      } catch (cleanupError) {
        console.error('[Delete] Error cleaning up preview file:', cleanupError);
        // Don't fail deletion if cleanup fails
      }
    }

    // Soft delete by marking isActive = -1
    content.isActive = -1;
    await content.save();

    res.status(200).json({
      success: true,
      data: {},
      message: "Content soft-deleted successfully",
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get seller content
// @route   GET /api/content/seller/me
// @access  Private/Seller
exports.getSellerContent = async (req, res, next) => {
  try {
    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 9;
    const startIndex = (page - 1) * limit;

    // Get total count first
    const total = await Content.countDocuments({
      seller: req.user.id,
      isActive: 1
    });

    // Get paginated content
    const content = await Content.find({
      seller: req.user.id,
      isActive: 1
    })
      .sort("-createdAt") // Sort by creation date in descending order (newest first)
      .skip(startIndex)
      .limit(limit)
      .lean(); // Use lean() to get plain objects instead of Mongoose documents

    // Convert ObjectIds and Dates to strings for proper JSON serialization
    const serializedContent = content.map(item => ({
      ...item,
      _id: item._id.toString(),
      seller: item.seller.toString(),
      createdAt: item.createdAt ? item.createdAt.toISOString() : null,
      lastUpdated: item.lastUpdated ? item.lastUpdated.toISOString() : null,
      publishedDate: item.publishedDate ? item.publishedDate.toISOString() : null,
      // Add id field for consistency with frontend expectations
      id: item._id.toString()
    }));

    // Calculate total pages
    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      success: true,
      count: total,
      totalPages,
      currentPage: page,
      data: serializedContent,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single seller content (seller can view their own content regardless of status)
// @route   GET /api/content/seller/:id
// @access  Private/Seller
exports.getSellerContentById = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id)
      .populate({
        path: "seller",
        select: "firstName lastName profileImage isVerified",
      })
      .lean(); // Use lean() to get plain objects instead of Mongoose documents

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is content seller or admin
    if (
      content.seller._id.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to view this content`,
          403
        )
      );
    }

    // Convert ObjectIds and Dates to strings for proper JSON serialization
    const serializedContent = {
      ...content,
      _id: content._id.toString(),
      seller: {
        ...content.seller,
        _id: content.seller._id.toString()
      },
      createdAt: content.createdAt ? content.createdAt.toISOString() : null,
      lastUpdated: content.lastUpdated ? content.lastUpdated.toISOString() : null,
      publishedDate: content.publishedDate ? content.publishedDate.toISOString() : null,
      // Add id field for consistency with frontend expectations
      id: content._id.toString()
    };

    res.status(200).json({
      success: true,
      data: serializedContent,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get content categories
// @route   GET /api/content/categories
// @access  Public
exports.getContentCategories = async (req, res, next) => {
  try {
    // Get unique sport types
    const sports = await Content.distinct("sport", {
      status: "Published",
      visibility: "Public",
    });

    // Get unique content types
    const contentTypes = await Content.distinct("contentType", {
      status: "Published",
      visibility: "Public",
    });

    // Get unique difficulty levels
    const difficultyLevels = await Content.distinct("difficulty", {
      status: "Published",
      visibility: "Public",
    });

    // Get price ranges including both fixed prices and auction base prices
    const priceStats = await Content.aggregate([
      {
        $match: {
          status: "Published",
          visibility: "Public",
          $or: [
            { price: { $exists: true, $ne: null, $gt: 0 } },
            { "auctionDetails.basePrice": { $exists: true, $ne: null, $gt: 0 } }
          ]
        },
      },
      {
        $addFields: {
          effectivePrice: {
            $cond: {
              if: { $and: [{ $ne: ["$saleType", "Fixed"] }, { $gt: ["$auctionDetails.basePrice", 0] }] },
              then: "$auctionDetails.basePrice",
              else: { $ifNull: ["$price", 0] }
            }
          }
        }
      },
      {
        $match: {
          effectivePrice: { $gt: 0 }
        }
      },
      {
        $group: {
          _id: null,
          minPrice: { $min: "$effectivePrice" },
          maxPrice: { $max: "$effectivePrice" },
          avgPrice: { $avg: "$effectivePrice" },
        },
      },
    ]);

    // Get popular tags
    const tagCounts = await Content.aggregate([
      {
        $match: {
          status: "Published",
          visibility: "Public",
          tags: { $exists: true, $ne: [] },
        },
      },
      { $unwind: "$tags" },
      {
        $group: {
          _id: "$tags",
          count: { $sum: 1 },
        },
      },
      { $sort: { count: -1 } },
      { $limit: 20 },
    ]);

    const popularTags = tagCounts.map((tag) => tag._id);

    res.status(200).json({
      success: true,
      data: {
        sports,
        contentTypes,
        difficultyLevels,
        priceRange: priceStats[0] || { minPrice: 0, maxPrice: 0, avgPrice: 0 },
        popularTags,
      },
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Toggle content active status
// @route   PUT /api/content/:id/toggle-status
// @access  Private/Seller
exports.toggleContentStatus = async (req, res, next) => {
  try {
    let content = await Content.findById(req.params.id);

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is content seller
    if (
      content.seller.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to update this content`,
          403
        )
      );
    }

    // Toggle the isActive status
    const newStatus = content.isActive === 1 ? 0 : 1;

    content = await Content.findByIdAndUpdate(
      req.params.id,
      { isActive: newStatus },
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      success: true,
      data: content,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get trending content
// @route   GET /api/content/trending
// @access  Public
exports.getTrendingContent = async (req, res, next) => {
  try {
    // Base query for available content (not sold or ended auctions)
    const availableContentQuery = {
      status: "Published",
      visibility: "Public",
      isActive: 1,
      $and: [
        {
          $or: [
            { isSold: { $ne: true } },
            { isSold: { $exists: false } }
          ]
        },
        {
          $or: [
            // Content is not auction type
            { saleType: "Fixed" },
            // Auction content that hasn't ended yet
            {
              $and: [
                { saleType: { $in: ["Auction", "Both"] } },
                {
                  $or: [
                    { auctionStatus: { $ne: "Ended" } },
                    { auctionStatus: { $exists: false } }
                  ]
                }
              ]
            }
          ]
        }
      ]
    };

    // Get content with highest ratings
    const topRated = await Content.find({
      ...availableContentQuery,
      averageRating: { $exists: true, $gte: 4 },
    })
      .sort("-averageRating")
      .limit(5)
      .populate({
        path: "seller",
        select: "firstName lastName profileImage isVerified",
      });

    // Get most recently published content
    const newest = await Content.find(availableContentQuery)
      .sort("-createdAt")
      .limit(5)
      .populate({
        path: "seller",
        select: "firstName lastName profileImage isVerified",
      });

    // Get most purchased content (would require aggregation with orders)
    // This is a placeholder - you would need to implement the actual query
    const popular = await Content.find(availableContentQuery)
      .sort("-createdAt")
      .limit(5)
      .populate({
        path: "seller",
        select: "firstName lastName profileImage isVerified",
      });

    res.status(200).json({
      success: true,
      data: {
        topRated,
        newest,
        popular,
      },
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get secure content file access
// @route   GET /api/content/:id/access
// @access  Private
exports.getContentAccess = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id);

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Check if content is published
    if (content.status !== "Published") {
      return next(
        new ErrorResponse(`Content is not available`, 404)
      );
    }

    // Get user's access level
    const userId = req.user ? req.user.id : null;
    const userRole = req.user ? req.user.role : null;

    const fileAccess = await getContentFileAccess(content, userId, userRole);

    // Return appropriate file URL based on access level
    if (!fileAccess.fileUrl) {
      return next(
        new ErrorResponse(`Access denied. You need to purchase this content to access it.`, 403)
      );
    }

    res.status(200).json({
      success: true,
      data: {
        contentId: content._id,
        accessType: fileAccess.accessType,
        fileUrl: fileAccess.fileUrl,
        reason: fileAccess.reason,
        canAccessFull: fileAccess.canAccessFull,
        hasPreview: fileAccess.hasPreview
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get content preview (public access)
// @route   GET /api/content/:id/preview
// @access  Public
exports.getContentPreview = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id);

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Check if content is published
    if (content.status !== "Published") {
      return next(
        new ErrorResponse(`Content is not available`, 404)
      );
    }

    // Check if preview exists
    if (!content.previewUrl) {
      return next(
        new ErrorResponse(`Preview not available for this content`, 404)
      );
    }

    res.status(200).json({
      success: true,
      data: {
        contentId: content._id,
        previewUrl: content.previewUrl,
        accessType: 'preview',
        message: 'This is a preview version. Purchase the content to access the full version.'
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Test preview generation (development only) - DISABLED
// @route   POST /api/content/test-preview
// @access  Private/Seller/Admin
exports.testPreviewGeneration = async (req, res, next) => {
  try {
    res.status(200).json({
      success: true,
      message: 'Preview generation testing disabled. Use chunked upload for video previews.'
    });
  } catch (err) {
    next(err);
  }
};
