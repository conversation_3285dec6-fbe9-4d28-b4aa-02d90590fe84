# S3 Chunked Upload Fixes

## Issues Identified

### 1. Preview Generation Error
**Error**: `Missing required key 'Key' in params`
**Cause**: The `validateAndExtractS3Key` function was called with only one parameter instead of two
**Fix**: Added the missing `bucketName` parameter

### 2. S3 EntityTooSmall Error  
**Error**: `Your proposed upload is smaller than the minimum allowed size`
**Cause**: S3 multipart upload requires each part to be at least 5MB (except the last part)
**Fix**: Implemented dual assembly strategy based on file size

## Fixes Applied

### Fix 1: S3 Key Extraction
**File**: `Backend/utils/previewGenerator.js`
```javascript
// Before (broken)
s3Key = validateAndExtractS3Key(s3ChunkUrl);

// After (fixed)
s3Key = validateAndExtractS3Key(s3ChunkUrl, bucketName);
```

### Fix 2: Dual Assembly Strategy
**File**: `Backend/utils/chunkAssembler.js`

Added intelligent assembly strategy:
- **Small files (<100MB)**: Simple concatenation (no multipart)
- **Large files (≥100MB)**: S3 multipart upload

```javascript
const totalSize = sortedChunks.reduce((sum, chunk) => sum + (chunk.size || 0), 0);
const MULTIPART_THRESHOLD = 100 * 1024 * 1024; // 100MB

if (totalSize < MULTIPART_THRESHOLD) {
  return await assembleChunksS3Simple(sortedChunks, finalFileName, fileType, uploadId);
} else {
  // Use multipart upload for large files
}
```

### Fix 3: Optimized Chunk Sizes
**Files**: `Frontend/src/config/uploadConfig.js` & `Backend/routes/content.js`

Updated chunk size calculation to avoid S3 multipart issues:

```javascript
// New chunk size logic
if (fileSize < 50 * 1024 * 1024) { // < 50MB
  // For small files, use larger chunks to avoid S3 multipart issues
  chunkSize = Math.max(5 * 1024 * 1024, Math.ceil(fileSize / 3)); // At least 5MB or file/3
} else if (fileSize < 100 * 1024 * 1024) { // < 100MB
  chunkSize = 5 * 1024 * 1024; // 5MB
} else if (fileSize < 500 * 1024 * 1024) { // < 500MB
  chunkSize = 10 * 1024 * 1024; // 10MB
} else { // >= 500MB
  chunkSize = 15 * 1024 * 1024; // 15MB
}

// Ensure minimum chunk size for S3 compatibility (except for very small files)
if (fileSize > 20 * 1024 * 1024) { // > 20MB
  chunkSize = Math.max(chunkSize, 5 * 1024 * 1024); // Minimum 5MB for S3
}
```

## New Assembly Flow

### For Small Files (<100MB):
1. **Download chunks** from S3 to temporary file
2. **Concatenate chunks** sequentially 
3. **Upload final file** to S3 as single object
4. **Clean up** temporary files and chunks

### For Large Files (≥100MB):
1. **Use S3 multipart upload** (existing logic)
2. **Copy chunks** as multipart pieces
3. **Complete multipart upload**
4. **Clean up** temporary chunks

## Benefits

### 1. Compatibility
- ✅ **Small files**: No more S3 EntityTooSmall errors
- ✅ **Large files**: Efficient multipart upload
- ✅ **All video sizes**: Proper chunked upload support

### 2. Performance
- ✅ **Optimized chunk sizes**: Fewer chunks for small files
- ✅ **S3 efficiency**: Uses appropriate upload method
- ✅ **Preview generation**: Fixed S3 key extraction

### 3. Reliability
- ✅ **Error handling**: Proper fallback strategies
- ✅ **Cleanup**: Temporary files and chunks removed
- ✅ **Logging**: Better error tracking

## Expected Results

### For Your 12MB Video:
1. **Chunk size**: ~4-5MB (2-3 chunks instead of 6 small chunks)
2. **Assembly**: Simple concatenation (no multipart)
3. **Preview**: First chunk processed correctly
4. **Database**: Both fileUrl and previewUrl saved

### Console Output Should Show:
```
[Upload] Using Chunked Upload: true
[Upload] Reason: Video file (requires first-chunk preview)
[ChunkAssembler] Total file size: 12MB
[ChunkAssembler] Using simple concatenation for small file
[Preview] First-chunk preview generated successfully
```

## Testing

1. **Upload the same 12MB video** again
2. **Check console logs** for the new assembly strategy
3. **Verify preview generation** works without S3 key errors
4. **Confirm database** has both URLs saved

The fixes ensure that all video files (regardless of size) use chunked upload with proper S3 compatibility and first-chunk preview generation.
