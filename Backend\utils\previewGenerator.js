const ffmpeg = require('fluent-ffmpeg');
const { PDFDocument } = require('pdf-lib');
const fs = require('fs');
const path = require('path');
const { getS3Instance, isUsingS3Storage } = require('./storageHelper');

// Configure ffmpeg path using ffmpeg-static for all environments
// This ensures FFmpeg is available even if not installed system-wide
try {
  const ffmpegStatic = require('ffmpeg-static');
  if (ffmpegStatic) {
    ffmpeg.setFfmpegPath(ffmpegStatic);
    console.log(`[Setup] FFmpeg path set to: ${ffmpegStatic}`);
  } else {
    console.warn('[Setup] ffmpeg-static returned null, will try to use system FFmpeg');
  }
} catch (error) {
  console.warn('[Setup] Failed to load ffmpeg-static, will try to use system FFmpeg:', error.message);
}

// Get S3 instance if credentials are available (cached instance)
const s3 = getS3Instance();

/**
 * Generate video preview from first chunk (fast preview for immediate user feedback)
 * @param {string} chunkPath - Path to the first video chunk
 * @param {string} outputFileName - Name for the preview file
 * @param {boolean} isS3Upload - Whether to upload to S3 or save locally
 * @returns {Promise<string>} - URL of the generated preview
 */
const generateVideoPreviewFromFirstChunk = async (chunkPath, outputFileName, isS3Upload = false) => {
  return new Promise((resolve, reject) => {
    try {
      const previewFileName = `${path.parse(outputFileName).name}_chunk_preview${path.parse(outputFileName).ext}`;

      console.log(`[Preview] Starting first-chunk video preview generation for: ${outputFileName}`);
      console.log(`[Preview] Chunk path: ${chunkPath}`);
      console.log(`[Preview] Is S3 upload: ${isS3Upload}`);

      if (isS3Upload && s3) {
        // For S3 uploads, process the chunk directly
        generateVideoPreviewFromChunkS3(chunkPath, previewFileName)
          .then((result) => {
            console.log(`[Preview] S3 first-chunk video preview generated successfully: ${result}`);
            resolve(result);
          })
          .catch((error) => {
            console.error(`[Preview] S3 first-chunk video preview generation failed:`, error);
            reject(error);
          });
      } else {
        // Local chunk processing
        const outputPath = path.join('./uploads/previews/', previewFileName);

        // Ensure preview directory exists
        const previewDir = path.dirname(outputPath);
        if (!fs.existsSync(previewDir)) {
          console.log(`[Preview] Creating preview directory: ${previewDir}`);
          fs.mkdirSync(previewDir, { recursive: true });
        }

        // Validate input chunk exists
        if (!fs.existsSync(chunkPath)) {
          const error = new Error(`Input video chunk not found: ${chunkPath}`);
          console.error(`[Preview] ${error.message}`);
          reject(error);
          return;
        }

        console.log(`[Preview] Processing first chunk to preview: ${outputPath}`);

        // Shorter timeout for chunk processing (1 minute)
        const ffmpegTimeout = setTimeout(() => {
          console.error(`[Preview] FFmpeg timeout after 1 minute for chunk: ${outputFileName}`);
          reject(new Error('First chunk preview generation timeout'));
        }, 60000); // 1 minute timeout

        ffmpeg(chunkPath)
          .setStartTime(0) // Start from beginning of chunk
          .setDuration(5) // 5 seconds duration (shorter for chunk)
          .videoCodec('libx264') // Ensure compatibility
          .audioCodec('aac') // Ensure audio compatibility
          .format('mp4') // Ensure MP4 format
          .size('?x360') // Lower resolution for faster processing (360p)
          .videoBitrate('300k') // Lower bitrate for faster processing
          .output(outputPath)
          .on('start', (commandLine) => {
            console.log(`[Preview] First-chunk FFmpeg command: ${commandLine}`);
          })
          .on('progress', (progress) => {
            console.log(`[Preview] First-chunk processing: ${Math.round(progress.percent || 0)}% done`);
          })
          .on('end', () => {
            clearTimeout(ffmpegTimeout);
            const previewUrl = `/uploads/previews/${previewFileName}`;
            console.log(`[Preview] First-chunk video preview generated successfully: ${previewUrl}`);

            // Verify the output file was created and has content
            if (fs.existsSync(outputPath)) {
              const stats = fs.statSync(outputPath);
              console.log(`[Preview] First-chunk preview file size: ${stats.size} bytes`);
              if (stats.size > 0) {
                resolve(previewUrl);
              } else {
                reject(new Error('Generated first-chunk preview file is empty'));
              }
            } else {
              reject(new Error('First-chunk preview file was not created'));
            }
          })
          .on('error', (err) => {
            clearTimeout(ffmpegTimeout);
            console.error(`[Preview] First-chunk FFmpeg error:`, err);
            reject(new Error(`Failed to generate first-chunk video preview: ${err.message}`));
          })
          .run();
      }
    } catch (error) {
      console.error(`[Preview] Error in generateVideoPreviewFromFirstChunk:`, error);
      reject(error);
    }
  });
};

/**
 * Generate video preview (10-second clip from the beginning) - Full video processing
 * @param {string} inputPath - Path to the original video file
 * @param {string} outputFileName - Name for the preview file
 * @param {boolean} isS3Upload - Whether to upload to S3 or save locally
 * @returns {Promise<string>} - URL of the generated preview
 */
const generateVideoPreview = async (inputPath, outputFileName, isS3Upload = false) => {
  return new Promise((resolve, reject) => {
    try {
      const previewFileName = `${path.parse(outputFileName).name}_preview${path.parse(outputFileName).ext}`;

      console.log(`[Preview] Starting video preview generation for: ${outputFileName}`);
      console.log(`[Preview] Input path: ${inputPath}`);
      console.log(`[Preview] Is S3 upload: ${isS3Upload}`);

      if (isS3Upload && s3) {
        // For S3 uploads, we need to download the file first, process it, then upload the preview
        generateVideoPreviewS3(inputPath, previewFileName)
          .then((result) => {
            console.log(`[Preview] S3 video preview generated successfully: ${result}`);
            resolve(result);
          })
          .catch((error) => {
            console.error(`[Preview] S3 video preview generation failed:`, error);
            reject(error);
          });
      } else {
        // Local file processing
        const outputPath = path.join('./uploads/previews/', previewFileName);

        // Ensure preview directory exists
        const previewDir = path.dirname(outputPath);
        if (!fs.existsSync(previewDir)) {
          console.log(`[Preview] Creating preview directory: ${previewDir}`);
          fs.mkdirSync(previewDir, { recursive: true });
        }

        // Validate input file exists
        if (!fs.existsSync(inputPath)) {
          const error = new Error(`Input video file not found: ${inputPath}`);
          console.error(`[Preview] ${error.message}`);
          reject(error);
          return;
        }

        console.log(`[Preview] Processing local video file to: ${outputPath}`);

        // Add timeout for FFmpeg processing
        const ffmpegTimeout = setTimeout(() => {
          console.error(`[Preview] FFmpeg timeout after 3 minutes for: ${outputFileName}`);
          reject(new Error('Video preview generation timeout'));
        }, 180000); // 3 minutes timeout

        ffmpeg(inputPath)
          .setStartTime(0) // Start from beginning
          .setDuration(10) // 10 seconds duration
          .videoCodec('libx264') // Ensure compatibility
          .audioCodec('aac') // Ensure audio compatibility
          .format('mp4') // Ensure MP4 format
          .size('?x480') // Reduce resolution to 480p for smaller file size
          .videoBitrate('500k') // Reduce bitrate for smaller file size
          .output(outputPath)
          .on('start', (commandLine) => {
            console.log(`[Preview] FFmpeg command: ${commandLine}`);
          })
          .on('progress', (progress) => {
            console.log(`[Preview] Processing: ${Math.round(progress.percent || 0)}% done`);
          })
          .on('end', () => {
            clearTimeout(ffmpegTimeout);
            const previewUrl = `/uploads/previews/${previewFileName}`;
            console.log(`[Preview] Video preview generated successfully: ${previewUrl}`);

            // Verify the output file was created and has content
            if (fs.existsSync(outputPath)) {
              const stats = fs.statSync(outputPath);
              console.log(`[Preview] Preview file size: ${stats.size} bytes`);
              if (stats.size > 0) {
                resolve(previewUrl);
              } else {
                reject(new Error('Generated preview file is empty'));
              }
            } else {
              reject(new Error('Preview file was not created'));
            }
          })
          .on('error', (err) => {
            clearTimeout(ffmpegTimeout);
            console.error(`[Preview] FFmpeg error:`, err);
            reject(new Error(`Failed to generate video preview: ${err.message}`));
          })
          .run();
      }
    } catch (error) {
      console.error(`[Preview] Error in generateVideoPreview:`, error);
      reject(error);
    }
  });
};

/**
 * Generate video preview from first chunk for S3 stored files
 * @param {string} s3ChunkUrl - S3 URL of the first video chunk
 * @param {string} previewFileName - Name for the preview file
 * @returns {Promise<string>} - S3 URL of the generated preview
 */
const generateVideoPreviewFromChunkS3 = async (s3ChunkUrl, previewFileName) => {
  return new Promise((resolve, reject) => {
    try {
      // Create temporary local paths
      const tempDir = './temp';
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const tempInputPath = path.join(tempDir, `chunk_input_${Date.now()}.mp4`);
      const tempOutputPath = path.join(tempDir, `chunk_preview_${Date.now()}.mp4`);

      console.log(`[Preview] Downloading first chunk from S3: ${s3ChunkUrl}`);

      // Download the chunk from S3
      const s3 = getS3Instance();
      const bucketName = process.env.AWS_BUCKET_NAME;

      // Extract S3 key from URL
      let s3Key;
      try {
        s3Key = validateAndExtractS3Key(s3ChunkUrl, bucketName);
      } catch (keyError) {
        console.error('[Preview] Error extracting S3 key:', keyError);
        reject(new Error(`Invalid S3 URL format: ${keyError.message}`));
        return;
      }

      const downloadParams = {
        Bucket: bucketName,
        Key: s3Key
      };

      const fileStream = fs.createWriteStream(tempInputPath);
      const s3Stream = s3.getObject(downloadParams).createReadStream();

      s3Stream.pipe(fileStream);

      s3Stream.on('error', (err) => {
        console.error('Error downloading chunk from S3:', err);
        reject(new Error(`Failed to download chunk from S3: ${err.message}`));
      });

      fileStream.on('close', () => {
        // Shorter timeout for chunk processing (1 minute)
        const s3FfmpegTimeout = setTimeout(() => {
          console.error(`[Preview] S3 FFmpeg timeout after 1 minute for chunk: ${previewFileName}`);
          // Clean up temporary files
          try {
            if (fs.existsSync(tempInputPath)) fs.unlinkSync(tempInputPath);
            if (fs.existsSync(tempOutputPath)) fs.unlinkSync(tempOutputPath);
          } catch (cleanupError) {
            console.error('[Preview] Error cleaning up temp files:', cleanupError);
          }
          reject(new Error('S3 first chunk preview generation timeout'));
        }, 60000); // 1 minute timeout

        // Process the downloaded chunk
        ffmpeg(tempInputPath)
          .setStartTime(0)
          .setDuration(5) // 5 seconds for chunk preview
          .videoCodec('libx264')
          .audioCodec('aac')
          .format('mp4')
          .size('?x360') // Lower resolution for faster processing
          .videoBitrate('300k') // Lower bitrate for faster processing
          .output(tempOutputPath)
          .on('start', (commandLine) => {
            console.log(`[Preview] S3 first-chunk FFmpeg command: ${commandLine}`);
          })
          .on('progress', (progress) => {
            console.log(`[Preview] S3 first-chunk processing: ${Math.round(progress.percent || 0)}% done`);
          })
          .on('end', () => {
            clearTimeout(s3FfmpegTimeout);
            // Upload preview to S3
            const uploadParams = {
              Bucket: bucketName,
              Key: `previews/${previewFileName}`,
              Body: fs.createReadStream(tempOutputPath),
              ContentType: 'video/mp4'
              // Removed ACL setting - bucket policy handles public access
            };

            s3.upload(uploadParams, (uploadErr, uploadData) => {
              // Clean up temporary files
              try {
                if (fs.existsSync(tempInputPath)) fs.unlinkSync(tempInputPath);
                if (fs.existsSync(tempOutputPath)) fs.unlinkSync(tempOutputPath);
              } catch (cleanupError) {
                console.error('[Preview] Error cleaning up temp files:', cleanupError);
              }

              if (uploadErr) {
                console.error('[Preview] Error uploading first-chunk preview to S3:', uploadErr);
                reject(new Error(`Failed to upload first-chunk preview to S3: ${uploadErr.message}`));
              } else {
                console.log(`[Preview] S3 first-chunk preview uploaded successfully: ${uploadData.Location}`);
                resolve(uploadData.Location);
              }
            });
          })
          .on('error', (err) => {
            clearTimeout(s3FfmpegTimeout);
            console.error(`[Preview] S3 first-chunk FFmpeg error:`, err);
            // Clean up temporary files
            try {
              if (fs.existsSync(tempInputPath)) fs.unlinkSync(tempInputPath);
              if (fs.existsSync(tempOutputPath)) fs.unlinkSync(tempOutputPath);
            } catch (cleanupError) {
              console.error('[Preview] Error cleaning up temp files:', cleanupError);
            }
            reject(new Error(`Failed to generate S3 first-chunk video preview: ${err.message}`));
          })
          .run();
      });

      fileStream.on('error', (err) => {
        console.error('Error downloading chunk from S3:', err);
        reject(new Error(`Failed to download chunk from S3: ${err.message}`));
      });

    } catch (error) {
      console.error('Error in generateVideoPreviewFromChunkS3:', error);
      reject(error);
    }
  });
};

/**
 * Generate video preview for S3 stored files - Full video processing
 * @param {string} s3Url - S3 URL of the original video
 * @param {string} previewFileName - Name for the preview file
 * @returns {Promise<string>} - S3 URL of the generated preview
 */
const generateVideoPreviewS3 = async (s3Url, previewFileName) => {
  return new Promise((resolve, reject) => {
    try {
      // Create temporary local paths
      const tempDir = './temp';
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const tempInputPath = path.join(tempDir, `temp_${Date.now()}_input.mp4`);
      const tempOutputPath = path.join(tempDir, `temp_${Date.now()}_preview.mp4`);

      // Download original file from S3
      const bucketName = process.env.AWS_BUCKET_NAME;

      // Extract and validate the S3 key from the URL
      const key = validateAndExtractS3Key(s3Url, bucketName);

      if (!key) {
        reject(new Error(`Failed to extract valid S3 key from URL: ${s3Url}`));
        return;
      }

      console.log(`[Preview] Downloading from S3 - Bucket: ${bucketName}, Key: ${key}`);
      console.log(`[Preview] Original S3 URL: ${s3Url}`);
      console.log(`[Preview] Extracted key parts:`, {
        urlParts: s3Url.split('/'),
        bucketName,
        extractedKey: key
      });

      const downloadParams = {
        Bucket: bucketName,
        Key: key
      };

      const fileStream = fs.createWriteStream(tempInputPath);

      // Add error handling for S3 stream
      let s3Stream;
      try {
        s3Stream = s3.getObject(downloadParams).createReadStream();
      } catch (s3Error) {
        console.error(`[Preview] Failed to create S3 read stream for key: ${key}`, s3Error);
        if (s3Error.code === 'NoSuchKey') {
          reject(new Error(`File not found in S3: ${key}. The file may have been moved or deleted.`));
        } else {
          reject(new Error(`S3 stream creation failed: ${s3Error.message}`));
        }
        return;
      }

      s3Stream.on('error', (s3Error) => {
        console.error(`[Preview] S3 stream error for key: ${key}`, s3Error);
        if (s3Error.code === 'NoSuchKey') {
          reject(new Error(`File not found in S3: ${key}. The file may have been moved or deleted.`));
        } else {
          reject(new Error(`S3 stream error: ${s3Error.message}`));
        }
      });

      s3Stream.pipe(fileStream);

      fileStream.on('close', () => {
        // Add timeout for S3 FFmpeg processing
        const s3FfmpegTimeout = setTimeout(() => {
          console.error(`[Preview] S3 FFmpeg timeout after 3 minutes for: ${previewFileName}`);
          // Clean up temporary files
          try {
            if (fs.existsSync(tempInputPath)) fs.unlinkSync(tempInputPath);
            if (fs.existsSync(tempOutputPath)) fs.unlinkSync(tempOutputPath);
          } catch (cleanupError) {
            console.error('[Preview] Error cleaning up temp files:', cleanupError);
          }
          reject(new Error('S3 video preview generation timeout'));
        }, 180000); // 3 minutes timeout

        // Process the downloaded file
        ffmpeg(tempInputPath)
          .setStartTime(0)
          .setDuration(10)
          .videoCodec('libx264')
          .audioCodec('aac')
          .format('mp4')
          .size('?x480')
          .videoBitrate('500k')
          .output(tempOutputPath)
          .on('start', (commandLine) => {
            console.log(`[Preview] S3 FFmpeg command: ${commandLine}`);
          })
          .on('progress', (progress) => {
            console.log(`[Preview] S3 Processing: ${Math.round(progress.percent || 0)}% done`);
          })
          .on('end', () => {
            clearTimeout(s3FfmpegTimeout);
            // Upload preview to S3
            const uploadParams = {
              Bucket: bucketName,
              Key: `previews/${previewFileName}`,
              Body: fs.createReadStream(tempOutputPath),
              ContentType: 'video/mp4'
              // Removed ACL setting - bucket policy handles public access
            };

            s3.upload(uploadParams, (err, data) => {
              // Clean up temporary files
              try {
                if (fs.existsSync(tempInputPath)) fs.unlinkSync(tempInputPath);
                if (fs.existsSync(tempOutputPath)) fs.unlinkSync(tempOutputPath);
              } catch (cleanupError) {
                console.error('[Preview] Error cleaning up temp files:', cleanupError);
              }

              if (err) {
                console.error('Error uploading preview to S3:', err);
                reject(new Error(`Failed to upload preview to S3: ${err.message}`));
              } else {
                console.log(`[Preview] S3 preview uploaded successfully: ${data.Location}`);
                resolve(data.Location);
              }
            });
          })
          .on('error', (err) => {
            clearTimeout(s3FfmpegTimeout);
            console.error(`[Preview] S3 FFmpeg error:`, err);
            // Clean up temporary files
            try {
              if (fs.existsSync(tempInputPath)) fs.unlinkSync(tempInputPath);
              if (fs.existsSync(tempOutputPath)) fs.unlinkSync(tempOutputPath);
            } catch (cleanupError) {
              console.error('[Preview] Error cleaning up temp files:', cleanupError);
            }
            reject(new Error(`Failed to generate S3 video preview: ${err.message}`));
          })
          .run();
      });

      fileStream.on('error', (err) => {
        console.error('Error downloading file from S3:', err);
        reject(new Error(`Failed to download file from S3: ${err.message}`));
      });

    } catch (error) {
      console.error('Error in generateVideoPreviewS3:', error);
      reject(error);
    }
  });
};

/**
 * Generate PDF preview (first page only)
 * @param {string} inputPath - Path to the original PDF file
 * @param {string} outputFileName - Name for the preview file
 * @param {boolean} isS3Upload - Whether to upload to S3 or save locally
 * @returns {Promise<string>} - URL of the generated preview
 */
const generatePdfPreview = async (inputPath, outputFileName, isS3Upload = false) => {
  try {
    const previewFileName = `${path.parse(outputFileName).name}_preview${path.parse(outputFileName).ext}`;

    console.log(`[Preview] Starting PDF preview generation for: ${outputFileName}`);
    console.log(`[Preview] Input path: ${inputPath}`);
    console.log(`[Preview] Is S3 upload: ${isS3Upload}`);

    if (isS3Upload && s3) {
      const result = await generatePdfPreviewS3(inputPath, previewFileName);
      console.log(`[Preview] S3 PDF preview generated successfully: ${result}`);
      return result;
    } else {
      // Local file processing
      const outputPath = path.join('./uploads/previews/', previewFileName);

      // Ensure preview directory exists
      const previewDir = path.dirname(outputPath);
      if (!fs.existsSync(previewDir)) {
        console.log(`[Preview] Creating preview directory: ${previewDir}`);
        fs.mkdirSync(previewDir, { recursive: true });
      }

      // Validate input file exists
      if (!fs.existsSync(inputPath)) {
        const error = new Error(`Input PDF file not found: ${inputPath}`);
        console.error(`[Preview] ${error.message}`);
        throw error;
      }

      console.log(`[Preview] Processing local PDF file to: ${outputPath}`);

      // Read the original PDF
      const existingPdfBytes = fs.readFileSync(inputPath);
      console.log(`[Preview] Original PDF size: ${existingPdfBytes.length} bytes`);

      const pdfDoc = await PDFDocument.load(existingPdfBytes);
      const pageCount = pdfDoc.getPageCount();
      console.log(`[Preview] PDF has ${pageCount} pages, extracting first page`);

      if (pageCount === 0) {
        throw new Error('PDF file has no pages');
      }

      // Create a new PDF with only the first page
      const newPdfDoc = await PDFDocument.create();
      const [firstPage] = await newPdfDoc.copyPages(pdfDoc, [0]);
      newPdfDoc.addPage(firstPage);

      // Save the preview PDF
      const pdfBytes = await newPdfDoc.save();
      fs.writeFileSync(outputPath, pdfBytes);

      console.log(`[Preview] Preview PDF size: ${pdfBytes.length} bytes`);

      // Verify the output file was created and has content
      if (fs.existsSync(outputPath)) {
        const stats = fs.statSync(outputPath);
        console.log(`[Preview] Preview file created successfully with size: ${stats.size} bytes`);
        if (stats.size === 0) {
          throw new Error('Generated preview file is empty');
        }
      } else {
        throw new Error('Preview file was not created');
      }

      const previewUrl = `/uploads/previews/${previewFileName}`;
      console.log(`[Preview] PDF preview generated successfully: ${previewUrl}`);
      return previewUrl;
    }
  } catch (error) {
    console.error(`[Preview] Error generating PDF preview:`, error);
    throw new Error(`Failed to generate PDF preview: ${error.message}`);
  }
};

/**
 * Generate PDF preview for S3 stored files
 * @param {string} s3Url - S3 URL of the original PDF
 * @param {string} previewFileName - Name for the preview file
 * @returns {Promise<string>} - S3 URL of the generated preview
 */
const generatePdfPreviewS3 = async (s3Url, previewFileName) => {
  try {
    // Download original PDF from S3
    const bucketName = process.env.AWS_BUCKET_NAME;

    // Extract and validate the S3 key from the URL
    const key = validateAndExtractS3Key(s3Url, bucketName);

    if (!key) {
      throw new Error(`Failed to extract valid S3 key from PDF URL: ${s3Url}`);
    }

    console.log(`[Preview] Downloading PDF from S3 - Bucket: ${bucketName}, Key: ${key}`);
    console.log(`[Preview] Original PDF S3 URL: ${s3Url}`);
    console.log(`[Preview] Extracted PDF key parts:`, {
      urlParts: s3Url.split('/'),
      bucketName,
      extractedKey: key
    });

    const downloadParams = {
      Bucket: bucketName,
      Key: key
    };

    // Add error handling for S3 PDF download
    let data;
    try {
      data = await s3.getObject(downloadParams).promise();
    } catch (s3Error) {
      console.error(`[Preview] S3 PDF download failed for key: ${key}`, s3Error);
      if (s3Error.code === 'NoSuchKey') {
        throw new Error(`PDF file not found in S3: ${key}. The file may have been moved or deleted.`);
      }
      throw new Error(`S3 PDF download failed: ${s3Error.message}`);
    }

    const existingPdfBytes = data.Body;

    // Process PDF
    const pdfDoc = await PDFDocument.load(existingPdfBytes);
    const newPdfDoc = await PDFDocument.create();
    const [firstPage] = await newPdfDoc.copyPages(pdfDoc, [0]);
    newPdfDoc.addPage(firstPage);

    const pdfBytes = await newPdfDoc.save();

    // Upload preview to S3
    const uploadParams = {
      Bucket: bucketName,
      Key: `previews/${previewFileName}`,
      Body: pdfBytes,
      ContentType: 'application/pdf'
      // Removed ACL setting - bucket policy handles public access
    };

    const uploadResult = await s3.upload(uploadParams).promise();
    return uploadResult.Location;

  } catch (error) {
    console.error('Error generating PDF preview for S3:', error);
    throw new Error(`Failed to generate PDF preview for S3: ${error.message}`);
  }
};

/**
 * Generate preview based on content type
 * @param {string} contentType - Type of content (Video, PDF, etc.)
 * @param {string} filePath - Path to the original file
 * @param {string} fileName - Original file name
 * @param {boolean} isS3Upload - Whether file is stored on S3 (optional, auto-detected if not provided)
 * @returns {Promise<string|null>} - URL of the generated preview or null if not supported
 */
const generatePreview = async (contentType, filePath, fileName, isS3Upload = null) => {
  // Auto-detect storage type if not explicitly provided
  if (isS3Upload === null) {
    isS3Upload = isUsingS3Storage();
  }
  try {
    console.log(`[Preview] Starting preview generation for ${contentType} file: ${fileName}`);

    // Get file extension once for all cases
    const fileExt = path.extname(fileName).toLowerCase();

    switch (contentType.toLowerCase()) {
      case 'video':
        // Check if it's a video file by extension
        const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'];

        if (videoExtensions.includes(fileExt)) {
          console.log(`[Preview] Generating video preview for: ${fileName}`);
          return await generateVideoPreview(filePath, fileName, isS3Upload);
        } else {
          console.log(`[Preview] Video file extension ${fileExt} not supported for preview generation`);
          return null;
        }

      case 'pdf':
      case 'document':
        if (fileExt === '.pdf') {
          console.log(`[Preview] Generating PDF preview for: ${fileName}`);
          return await generatePdfPreview(filePath, fileName, isS3Upload);
        } else {
          console.log(`[Preview] Non-PDF document detected: ${fileName} (${fileExt})`);
          console.log(`[Preview] Document preview will use original file for display`);

          // For non-Office documents, return the original file URL as preview
          if (isS3Upload) {
            // For S3 files, return the S3 URL directly
            console.log(`[Preview] Returning S3 URL for document preview: ${filePath}`);
            return filePath;
          } else {
            // For local files, ensure proper URL format
            // If filePath is already a proper URL path (starts with /uploads), use it
            if (filePath.startsWith('/uploads/')) {
              console.log(`[Preview] Returning existing URL path for document preview: ${filePath}`);
              return filePath;
            } else {
              // If filePath is a local file path, convert to URL format
              const normalizedPath = filePath.replace(/\\/g, '/'); // Convert backslashes to forward slashes
              let urlPath;

              if (normalizedPath.startsWith('./uploads/')) {
                urlPath = normalizedPath.substring(1); // Remove leading dot
              } else if (normalizedPath.startsWith('uploads/')) {
                urlPath = '/' + normalizedPath; // Add leading slash
              } else {
                // Assume it's just the filename and construct the full path
                urlPath = `/uploads/${fileName}`;
              }

              console.log(`[Preview] Converted local path to URL for document preview: ${urlPath}`);
              return urlPath;
            }
          }
        }

      default:
        console.log(`[Preview] Preview generation not supported for content type: ${contentType}`);
        return null;
    }
  } catch (error) {
    console.error(`[Preview] Error in generatePreview for ${fileName}:`, error);
    console.error(`[Preview] Error details:`, {
      contentType,
      fileName,
      filePath,
      isS3Upload,
      errorMessage: error.message,
      errorStack: error.stack
    });
    // Don't throw error - just log it and return null so content creation doesn't fail
    return null;
  }
};



/**
 * Validate file type for preview generation
 * @param {string} contentType - Content type
 * @param {string} fileName - File name
 * @returns {boolean} - Whether preview can be generated
 */
const canGeneratePreview = (contentType, fileName) => {
  const fileExt = path.extname(fileName).toLowerCase();

  switch (contentType.toLowerCase()) {
    case 'video':
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'];
      return videoExtensions.includes(fileExt);

    case 'pdf':
    case 'document':
      // Support document types - only PDF supported
      const documentExtensions = ['.pdf'];
      return documentExtensions.includes(fileExt);

    default:
      return false;
  }
};

/**
 * Clean up preview files when content is deleted
 * @param {string} previewUrl - URL of the preview file to delete
 * @param {boolean} isS3Upload - Whether the file is stored on S3 (optional, auto-detected if not provided)
 * @returns {Promise<boolean>} - Whether cleanup was successful
 */
const cleanupPreviewFile = async (previewUrl, isS3Upload = null) => {
  // Auto-detect storage type if not explicitly provided
  if (isS3Upload === null) {
    const { isS3Url } = require('./storageHelper');
    isS3Upload = isS3Url(previewUrl);
  }
  try {
    if (!previewUrl) {
      console.log('[Cleanup] No preview URL provided, skipping cleanup');
      return true;
    }

    console.log(`[Cleanup] Starting preview file cleanup for: ${previewUrl}`);

    if (isS3Upload && s3) {
      // S3 cleanup
      const bucketName = process.env.AWS_BUCKET_NAME;

      // Extract the full S3 key from the preview URL
      let key;
      if (previewUrl.includes('amazonaws.com')) {
        // For standard S3 URLs, extract everything after the bucket name
        const urlParts = previewUrl.split('/');
        const bucketIndex = urlParts.findIndex(part => part.includes(bucketName));
        if (bucketIndex !== -1) {
          key = urlParts.slice(bucketIndex + 1).join('/');
        } else {
          // Fallback: assume everything after the domain is the key
          const domainIndex = urlParts.findIndex(part => part.includes('amazonaws.com'));
          key = urlParts.slice(domainIndex + 1).join('/');
        }
      } else {
        // For relative URLs like /uploads/previews/filename.ext
        const fileName = previewUrl.split('/').pop();
        key = `previews/${fileName}`;
      }

      const deleteParams = {
        Bucket: bucketName,
        Key: key
      };

      try {
        await s3.deleteObject(deleteParams).promise();
        console.log(`[Cleanup] S3 preview file deleted successfully: ${key}`);
        return true;
      } catch (s3Error) {
        console.error(`[Cleanup] S3 delete failed for key: ${key}`, s3Error);
        if (s3Error.code === 'NoSuchKey') {
          console.log(`[Cleanup] File already deleted or doesn't exist: ${key}`);
          return true; // Consider this a success since the file is gone
        }
        throw s3Error;
      }
    } else {
      // Local file cleanup
      const fileName = previewUrl.split('/').pop();
      const filePath = path.join('./uploads/previews/', fileName);

      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`[Cleanup] Local preview file deleted successfully: ${filePath}`);
        return true;
      } else {
        console.log(`[Cleanup] Preview file not found, may have been already deleted: ${filePath}`);
        return true;
      }
    }
  } catch (error) {
    console.error(`[Cleanup] Error cleaning up preview file:`, error);
    // Don't throw error - cleanup failure shouldn't prevent content deletion
    return false;
  }
};

/**
 * Validate and extract S3 key from URL
 * @param {string} s3Url - S3 URL to validate
 * @param {string} bucketName - Expected bucket name
 * @returns {string|null} - Extracted S3 key or null if invalid
 */
const validateAndExtractS3Key = (s3Url, bucketName) => {
  try {
    if (!s3Url || !bucketName) {
      console.error('[S3] Missing URL or bucket name');
      return null;
    }

    console.log(`[S3] Validating URL: ${s3Url}`);
    console.log(`[S3] Expected bucket: ${bucketName}`);

    // Check if it's a valid S3 URL
    if (!s3Url.includes('amazonaws.com')) {
      console.error('[S3] URL does not contain amazonaws.com');
      return null;
    }

    let key;
    const urlParts = s3Url.split('/');

    // Method 1: Find bucket name in URL parts
    const bucketIndex = urlParts.findIndex(part => part.includes(bucketName));
    if (bucketIndex !== -1) {
      key = urlParts.slice(bucketIndex + 1).join('/');
      console.log(`[S3] Key extracted using bucket name method: ${key}`);
    } else {
      // Method 2: Find amazonaws.com and extract everything after
      const domainIndex = urlParts.findIndex(part => part.includes('amazonaws.com'));
      if (domainIndex !== -1) {
        key = urlParts.slice(domainIndex + 1).join('/');
        console.log(`[S3] Key extracted using domain method: ${key}`);
      } else {
        // Method 3: Fallback - assume last 3 parts are the key
        key = urlParts.slice(-3).join('/');
        console.log(`[S3] Key extracted using fallback method: ${key}`);
      }
    }

    // Validate the extracted key
    if (!key || key.length === 0) {
      console.error('[S3] Extracted key is empty');
      return null;
    }

    // Remove any leading slashes
    key = key.replace(/^\/+/, '');

    console.log(`[S3] Final validated key: ${key}`);
    return key;

  } catch (error) {
    console.error('[S3] Error validating S3 URL:', error);
    return null;
  }
};

/**
 * Ensure preview directories exist
 * @returns {void}
 */
const ensurePreviewDirectories = () => {
  try {
    const previewDir = './uploads/previews/';
    const tempDir = './temp/';

    if (!fs.existsSync(previewDir)) {
      console.log(`[Setup] Creating preview directory: ${previewDir}`);
      fs.mkdirSync(previewDir, { recursive: true });
    }

    if (!fs.existsSync(tempDir)) {
      console.log(`[Setup] Creating temp directory: ${tempDir}`);
      fs.mkdirSync(tempDir, { recursive: true });
    }
  } catch (error) {
    console.error('[Setup] Error creating directories:', error);
  }
};

module.exports = {
  generatePreview,
  generateVideoPreview,
  generateVideoPreviewFromFirstChunk,
  generatePdfPreview,
  canGeneratePreview,
  cleanupPreviewFile,
  ensurePreviewDirectories,
  validateAndExtractS3Key
};
