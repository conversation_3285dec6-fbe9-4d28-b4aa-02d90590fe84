const fs = require('fs');
const path = require('path');
const { getS3Instance, hasAWSCredentials, getFileUrl } = require('./storageHelper');

/**
 * Assemble uploaded chunks into a final file
 * @param {Object} uploadSession - Upload session containing chunk information
 * @returns {Object} - Result with final file URL and metadata
 */
const assembleChunks = async (uploadSession) => {
  const { uploadId, fileName, fileSize, fileType, uploadedChunks, totalChunks } = uploadSession;
  
  console.log(`[ChunkAssembler] Starting assembly for ${fileName} (${totalChunks} chunks)`);

  // Sort chunks by index to ensure correct order
  const sortedChunks = uploadedChunks
    .filter(chunk => chunk) // Remove empty slots
    .sort((a, b) => a.chunkIndex - b.chunkIndex);

  if (sortedChunks.length !== totalChunks) {
    throw new Error(`Missing chunks: expected ${totalChunks}, got ${sortedChunks.length}`);
  }

  // Debug: Log first few chunk paths to understand the URL structure
  console.log(`[ChunkAssembler] Sample chunk paths:`);
  sortedChunks.slice(0, 3).forEach((chunk, index) => {
    console.log(`  Chunk ${chunk.chunkIndex}: ${chunk.tempPath}`);
  });
  
  const timestamp = Date.now();
  const sanitizedName = fileName.replace(/[^a-zA-Z0-9.-]/g, '-');
  const finalFileName = `${timestamp}-${sanitizedName}`;
  
  if (hasAWSCredentials()) {
    return await assembleChunksS3(sortedChunks, finalFileName, fileType, uploadId);
  } else {
    return await assembleChunksLocal(sortedChunks, finalFileName, fileType, uploadId);
  }
};

/**
 * Assemble chunks for S3 storage using optimized copy operations
 */
const assembleChunksS3 = async (sortedChunks, finalFileName, fileType, uploadId) => {
  const s3 = getS3Instance();
  if (!s3) {
    throw new Error('S3 not configured');
  }

  console.log(`[ChunkAssembler] Assembling ${sortedChunks.length} chunks for S3 upload using optimized copy operations`);

  // Calculate total file size to determine assembly strategy
  const totalSize = sortedChunks.reduce((sum, chunk) => sum + (chunk.size || 0), 0);
  const MULTIPART_THRESHOLD = 100 * 1024 * 1024; // 100MB threshold for multipart

  // Debug chunk sizes
  console.log(`[ChunkAssembler] Chunk sizes:`, sortedChunks.map(c => `${c.chunkIndex}: ${c.size || 'undefined'} bytes`));
  console.log(`[ChunkAssembler] Total file size calculated: ${Math.round(totalSize / (1024 * 1024))}MB`);

  // If calculated size is 0, use a fallback approach for small files
  const shouldUseSimpleAssembly = totalSize === 0 || totalSize < MULTIPART_THRESHOLD;

  if (shouldUseSimpleAssembly) {
    console.log(`[ChunkAssembler] Using simple concatenation (calculated size: ${totalSize} bytes)`);
  } else {
    console.log(`[ChunkAssembler] Using multipart upload (calculated size: ${totalSize} bytes)`);
  }

  // Use the determined assembly strategy
  if (shouldUseSimpleAssembly) {
    return await assembleChunksS3Simple(sortedChunks, finalFileName, fileType, uploadId);
  }

  // For large files, use multipart upload
  const s3Key = `uploads/content/${finalFileName}`;

  const createParams = {
    Bucket: process.env.AWS_BUCKET_NAME,
    Key: s3Key,
    ContentType: fileType,
    Metadata: {
      originalName: finalFileName,
      uploadId: uploadId,
      assembledAt: Date.now().toString()
    }
  };

  let multipartUpload = null;

  try {
    multipartUpload = await s3.createMultipartUpload(createParams).promise();
    const uploadId_s3 = multipartUpload.UploadId;

    console.log(`[ChunkAssembler] Created S3 multipart upload: ${uploadId_s3}`);

    // Set timeout for the entire assembly process (10 minutes max)
    const assemblyTimeout = setTimeout(() => {
      console.error(`[ChunkAssembler] Assembly timeout after 10 minutes for ${sortedChunks.length} chunks`);
      throw new Error('Chunk assembly timeout - process took too long');
    }, 600000); // 10 minutes

    // Process chunks in batches to optimize memory usage and performance
    const BATCH_SIZE = 5; // Process 5 chunks at a time
    const parts = [];

    for (let i = 0; i < sortedChunks.length; i += BATCH_SIZE) {
      const batch = sortedChunks.slice(i, i + BATCH_SIZE);
      console.log(`[ChunkAssembler] Processing batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(sortedChunks.length / BATCH_SIZE)} (chunks ${i + 1}-${Math.min(i + BATCH_SIZE, sortedChunks.length)})`);

      const batchPromises = batch.map(async (chunk, batchIndex) => {
        const index = i + batchIndex;
        const partNumber = index + 1;

        if (chunk.tempPath.startsWith('https://') || chunk.tempPath.includes('amazonaws.com')) {
          // Chunk is already in S3 - use copyPart for efficiency
          const url = new URL(chunk.tempPath);
          const chunkKey = url.pathname.substring(1); // Remove leading slash

          console.log(`[ChunkAssembler] Copying chunk ${index + 1} from S3 key: ${chunkKey}`);

          const copyParams = {
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: s3Key,
            PartNumber: partNumber,
            UploadId: uploadId_s3,
            CopySource: `${process.env.AWS_BUCKET_NAME}/${chunkKey}`
          };

          try {
            const result = await s3.uploadPartCopy(copyParams).promise();
            console.log(`[ChunkAssembler] Copied part ${partNumber}/${sortedChunks.length} from S3`);

            return {
              ETag: result.CopyPartResult.ETag,
              PartNumber: partNumber
            };
          } catch (error) {
            console.error(`[ChunkAssembler] Error copying chunk ${index + 1} from S3:`, error.code, error.message);
            console.error(`[ChunkAssembler] Chunk URL: ${chunk.tempPath}`);
            console.error(`[ChunkAssembler] Extracted key: ${chunkKey}`);
            console.error(`[ChunkAssembler] Copy source: ${process.env.AWS_BUCKET_NAME}/${chunkKey}`);

            // Fallback to download and upload if copy fails
            console.log(`[ChunkAssembler] Falling back to download/upload for chunk ${index + 1}`);
            return await downloadAndUploadChunk(s3, chunk, index, s3Key, partNumber, uploadId_s3);
          }
        } else {
          // Chunk is local file - upload directly
          const chunkData = fs.readFileSync(chunk.tempPath);
          console.log(`[ChunkAssembler] Uploading local chunk ${index + 1} (${chunkData.length} bytes)`);

          const uploadPartParams = {
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: s3Key,
            PartNumber: partNumber,
            UploadId: uploadId_s3,
            Body: chunkData
          };

          const result = await s3.uploadPart(uploadPartParams).promise();
          console.log(`[ChunkAssembler] Uploaded part ${partNumber}/${sortedChunks.length}`);

          return {
            ETag: result.ETag,
            PartNumber: partNumber
          };
        }
      });

      const batchParts = await Promise.all(batchPromises);
      parts.push(...batchParts);

      // Small delay between batches to prevent overwhelming S3
      if (i + BATCH_SIZE < sortedChunks.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // Clear the timeout since we completed successfully
    clearTimeout(assemblyTimeout);

    // Complete the multipart upload
    const completeParams = {
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: s3Key,
      UploadId: uploadId_s3,
      MultipartUpload: {
        Parts: parts
      }
    };

    const finalResult = await s3.completeMultipartUpload(completeParams).promise();

    console.log(`[ChunkAssembler] S3 assembly completed: ${finalResult.Location}`);

    // Clean up temporary chunks after successful assembly
    console.log(`[ChunkAssembler] Starting cleanup of ${sortedChunks.length} temporary chunks`);
    const cleanupResults = await cleanupTempChunks(sortedChunks);

    if (cleanupResults.failed > 0) {
      console.warn(`[ChunkAssembler] Warning: ${cleanupResults.failed} chunks failed to clean up, but assembly was successful`);
    } else {
      console.log(`[ChunkAssembler] All ${cleanupResults.successful} chunks cleaned up successfully`);
    }

    return {
      fileUrl: finalResult.Location,
      bucket: finalResult.Bucket,
      key: finalResult.Key,
      etag: finalResult.ETag,
      cleanupResults: cleanupResults
    };

  } catch (error) {
    console.error('[ChunkAssembler] S3 assembly error:', error);

    // Clear timeout on error
    if (typeof assemblyTimeout !== 'undefined') {
      clearTimeout(assemblyTimeout);
    }

    // Clean up failed multipart upload if it exists
    if (multipartUpload && multipartUpload.UploadId) {
      try {
        await s3.abortMultipartUpload({
          Bucket: process.env.AWS_BUCKET_NAME,
          Key: s3Key,
          UploadId: multipartUpload.UploadId
        }).promise();
        console.log('[ChunkAssembler] Aborted failed multipart upload');
      } catch (abortError) {
        console.error('[ChunkAssembler] Error aborting multipart upload:', abortError);
      }
    }

    throw error;
  }
};

/**
 * Fallback function to download and upload chunk if copy fails
 */
const downloadAndUploadChunk = async (s3, chunk, index, s3Key, partNumber, uploadId_s3) => {
  const url = new URL(chunk.tempPath);
  const chunkKey = url.pathname.substring(1);

  const getParams = {
    Bucket: process.env.AWS_BUCKET_NAME,
    Key: chunkKey
  };

  const chunkObject = await s3.getObject(getParams).promise();
  const chunkData = chunkObject.Body;
  console.log(`[ChunkAssembler] Downloaded chunk ${index + 1} as fallback (${chunkData.length} bytes)`);

  const uploadPartParams = {
    Bucket: process.env.AWS_BUCKET_NAME,
    Key: s3Key,
    PartNumber: partNumber,
    UploadId: uploadId_s3,
    Body: chunkData
  };

  const result = await s3.uploadPart(uploadPartParams).promise();
  console.log(`[ChunkAssembler] Uploaded part ${partNumber} via fallback method`);

  return {
    ETag: result.ETag,
    PartNumber: partNumber
  };
};

/**
 * Assemble small chunks for S3 using simple concatenation (for files < 100MB)
 */
const assembleChunksS3Simple = async (sortedChunks, finalFileName, fileType, uploadId) => {
  const s3 = getS3Instance();
  const bucketName = process.env.AWS_BUCKET_NAME;
  const s3Key = `uploads/content/${finalFileName}`;

  console.log(`[ChunkAssembler] Simple S3 assembly for ${sortedChunks.length} chunks`);

  try {
    // Create a temporary file to concatenate chunks
    const tempDir = './temp';
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const tempFilePath = path.join(tempDir, `temp_${Date.now()}_${finalFileName}`);
    const writeStream = fs.createWriteStream(tempFilePath);

    // Download and concatenate each chunk
    for (let i = 0; i < sortedChunks.length; i++) {
      const chunk = sortedChunks[i];
      console.log(`[ChunkAssembler] Processing chunk ${i + 1}/${sortedChunks.length}`);

      // Extract S3 key from chunk URL using proper extraction logic
      let chunkKey;
      try {
        // Use the same logic as in preview generator
        const { validateAndExtractS3Key } = require('./previewGenerator');
        chunkKey = validateAndExtractS3Key(chunk.tempPath, bucketName);
      } catch (keyError) {
        console.error(`[ChunkAssembler] Error extracting S3 key from ${chunk.tempPath}:`, keyError);
        throw new Error(`Invalid S3 URL format: ${keyError.message}`);
      }

      console.log(`[ChunkAssembler] Downloading chunk from S3 key: ${chunkKey}`);

      const downloadParams = {
        Bucket: bucketName,
        Key: chunkKey
      };

      const chunkData = await s3.getObject(downloadParams).promise();
      writeStream.write(chunkData.Body);
    }

    writeStream.end();

    // Wait for write stream to finish
    await new Promise((resolve, reject) => {
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });

    // Upload the concatenated file to S3
    const uploadParams = {
      Bucket: bucketName,
      Key: s3Key,
      Body: fs.createReadStream(tempFilePath),
      ContentType: fileType,
      Metadata: {
        originalName: finalFileName,
        uploadId: uploadId,
        assembledAt: Date.now().toString()
      }
    };

    const uploadResult = await s3.upload(uploadParams).promise();

    // Clean up temporary file
    try {
      fs.unlinkSync(tempFilePath);
    } catch (cleanupError) {
      console.error('[ChunkAssembler] Error cleaning up temp file:', cleanupError);
    }

    console.log(`[ChunkAssembler] Simple S3 assembly completed: ${uploadResult.Location}`);

    // Clean up temporary chunks
    const cleanupResults = await cleanupTempChunks(sortedChunks);

    return {
      fileUrl: uploadResult.Location,
      cleanupResults: cleanupResults
    };

  } catch (error) {
    console.error('[ChunkAssembler] Simple S3 assembly error:', error);
    throw error;
  }
};

/**
 * Assemble chunks for local storage
 */
const assembleChunksLocal = async (sortedChunks, finalFileName, fileType, uploadId) => {
  console.log(`[ChunkAssembler] Assembling ${sortedChunks.length} chunks for local storage`);
  
  const uploadsDir = './uploads';
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
  }
  
  const finalFilePath = path.join(uploadsDir, finalFileName);
  const writeStream = fs.createWriteStream(finalFilePath);
  
  try {
    // Write chunks in order
    for (let i = 0; i < sortedChunks.length; i++) {
      const chunk = sortedChunks[i];
      console.log(`[ChunkAssembler] Writing chunk ${i + 1}/${sortedChunks.length}`);
      
      const chunkData = fs.readFileSync(chunk.tempPath);
      writeStream.write(chunkData);
    }
    
    writeStream.end();
    
    // Wait for write to complete
    await new Promise((resolve, reject) => {
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });

    console.log(`[ChunkAssembler] Local assembly completed: ${finalFilePath}`);

    // Clean up temporary chunks after successful assembly
    console.log(`[ChunkAssembler] Starting cleanup of ${sortedChunks.length} temporary chunks`);
    const cleanupResults = await cleanupTempChunks(sortedChunks);

    if (cleanupResults.failed > 0) {
      console.warn(`[ChunkAssembler] Warning: ${cleanupResults.failed} chunks failed to clean up, but assembly was successful`);
    } else {
      console.log(`[ChunkAssembler] All ${cleanupResults.successful} chunks cleaned up successfully`);
    }

    return {
      fileUrl: `/uploads/${finalFileName}`,
      filename: finalFileName,
      path: finalFilePath,
      cleanupResults: cleanupResults
    };
    
  } catch (error) {
    console.error('[ChunkAssembler] Local assembly error:', error);
    
    // Clean up partial file
    if (fs.existsSync(finalFilePath)) {
      fs.unlinkSync(finalFilePath);
    }
    
    throw error;
  }
};

/**
 * Clean up temporary chunk files with enhanced verification and retry
 */
const cleanupTempChunks = async (chunks) => {
  console.log(`[ChunkAssembler] Cleaning up ${chunks.length} temporary chunks`);

  const s3 = getS3Instance();
  const cleanupResults = {
    total: chunks.length,
    successful: 0,
    failed: 0,
    errors: []
  };

  for (const chunk of chunks) {
    const maxRetries = 3;
    let retryCount = 0;
    let success = false;

    while (retryCount < maxRetries && !success) {
      try {
        if (chunk.tempPath.startsWith('https://') || chunk.tempPath.includes('amazonaws.com')) {
          // S3 chunk - delete from S3
          if (s3) {
            const url = new URL(chunk.tempPath);
            const chunkKey = url.pathname.substring(1); // Remove leading slash

            // First verify the object exists
            try {
              await s3.headObject({
                Bucket: process.env.AWS_BUCKET_NAME,
                Key: chunkKey
              }).promise();

              // Object exists, proceed with deletion
              await s3.deleteObject({
                Bucket: process.env.AWS_BUCKET_NAME,
                Key: chunkKey
              }).promise();

              // Verify deletion was successful
              try {
                await s3.headObject({
                  Bucket: process.env.AWS_BUCKET_NAME,
                  Key: chunkKey
                }).promise();
                // If we reach here, object still exists
                throw new Error('Object still exists after deletion');
              } catch (verifyError) {
                if (verifyError.code === 'NotFound' || verifyError.statusCode === 404) {
                  // Object successfully deleted
                  console.log(`[ChunkAssembler] Successfully deleted S3 chunk: ${chunkKey}`);
                  success = true;
                  cleanupResults.successful++;
                } else {
                  throw verifyError;
                }
              }
            } catch (headError) {
              if (headError.code === 'NotFound' || headError.statusCode === 404) {
                // Object doesn't exist, consider it already cleaned up
                console.log(`[ChunkAssembler] S3 chunk already deleted: ${chunkKey}`);
                success = true;
                cleanupResults.successful++;
              } else {
                throw headError;
              }
            }
          } else {
            console.warn(`[ChunkAssembler] S3 not available for chunk cleanup: ${chunk.tempPath}`);
            success = true; // Skip S3 chunks if S3 not available
            cleanupResults.successful++;
          }
        } else {
          // Local chunk - delete file
          if (fs.existsSync(chunk.tempPath)) {
            fs.unlinkSync(chunk.tempPath);

            // Verify deletion
            if (!fs.existsSync(chunk.tempPath)) {
              console.log(`[ChunkAssembler] Successfully deleted local chunk: ${chunk.tempPath}`);
              success = true;
              cleanupResults.successful++;
            } else {
              throw new Error('File still exists after deletion');
            }
          } else {
            console.log(`[ChunkAssembler] Local chunk already deleted: ${chunk.tempPath}`);
            success = true;
            cleanupResults.successful++;
          }
        }
      } catch (error) {
        retryCount++;
        console.error(`[ChunkAssembler] Error cleaning up chunk ${chunk.tempPath} (attempt ${retryCount}/${maxRetries}):`, error.message);

        if (retryCount < maxRetries) {
          // Wait before retry (exponential backoff)
          const delay = Math.pow(2, retryCount) * 1000; // 2s, 4s, 8s
          console.log(`[ChunkAssembler] Retrying chunk cleanup in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          // Max retries reached
          cleanupResults.failed++;
          cleanupResults.errors.push({
            chunkPath: chunk.tempPath,
            error: error.message
          });
        }
      }
    }
  }

  console.log(`[ChunkAssembler] Cleanup completed: ${cleanupResults.successful}/${cleanupResults.total} chunks cleaned successfully`);

  if (cleanupResults.failed > 0) {
    console.warn(`[ChunkAssembler] ${cleanupResults.failed} chunks failed to clean up:`, cleanupResults.errors);
  }

  return cleanupResults;
};

module.exports = {
  assembleChunks,
  cleanupTempChunks
};
