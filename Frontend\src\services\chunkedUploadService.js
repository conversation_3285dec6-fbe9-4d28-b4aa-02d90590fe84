import api from './api';
import {
  getUploadConfig,
  isChunkedUploadSupported,
  getOptimalChunkSize,
  CHUNKED_UPLOAD_CONFIG,
  ERROR_CONFIG
} from '../config/uploadConfig';

/**
 * Chunked Upload Service
 * Handles large file uploads by splitting them into smaller chunks
 */

class ChunkedUploadService {
  constructor() {
    this.activeUploads = new Map();
  }

  /**
   * Upload a file using chunked upload
   * @param {File} file - The file to upload
   * @param {string} contentType - Content type (Video, Document, etc.)
   * @param {Function} onProgress - Progress callback (progress, speed, eta)
   * @param {Function} onError - Error callback
   * @param {Function} onRetry - Retry callback
   * @returns {Promise} Upload result
   */
  async uploadFile(file, contentType, onProgress, onError, onRetry) {
    const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    try {
      // Check browser support
      if (!isChunkedUploadSupported()) {
        throw new Error('Chunked upload not supported in this browser');
      }

      // Get upload configuration
      const config = getUploadConfig(file, contentType);
      const chunkSize = getOptimalChunkSize(file.size);

      // Calculate chunks
      const totalChunks = Math.ceil(file.size / chunkSize);
      const chunks = this.createChunks(file, totalChunks, chunkSize);
      
      console.log(`[ChunkedUpload] Starting upload: ${file.name} (${totalChunks} chunks)`);
      
      // Initialize upload session
      const initResponse = await this.initializeUpload(file, totalChunks);
      const serverUploadId = initResponse.data.uploadId;
      
      // Track upload state
      const uploadState = {
        uploadId,
        serverUploadId,
        file,
        chunks,
        totalChunks,
        uploadedChunks: new Set(),
        failedChunks: new Map(),
        startTime: Date.now(),
        lastProgressTime: Date.now(),
        uploadedBytes: 0,
        onProgress,
        onError,
        onRetry
      };

      console.log(`[ChunkedUpload] Created upload state with serverUploadId: ${serverUploadId}`);
      
      this.activeUploads.set(uploadId, uploadState);
      
      // Upload chunks
      const result = await this.uploadChunks(uploadState);
      
      // Complete upload
      const finalResult = await this.completeUpload(serverUploadId);

      // Include first-chunk preview URL if available
      if (uploadState.firstChunkPreviewUrl && !finalResult.data.previewUrl) {
        finalResult.data.previewUrl = uploadState.firstChunkPreviewUrl;
        finalResult.data.isFirstChunkPreview = true;
      }

      // Cleanup
      this.activeUploads.delete(uploadId);

      console.log(`[ChunkedUpload] Upload completed: ${file.name}`);
      return finalResult;
      
    } catch (error) {
      console.error('[ChunkedUpload] Upload failed:', error);
      this.activeUploads.delete(uploadId);
      throw error;
    }
  }

  /**
   * Resume a failed upload
   * @param {string} uploadId - Upload ID to resume
   * @returns {Promise} Upload result
   */
  async resumeUpload(uploadId) {
    const uploadState = this.activeUploads.get(uploadId);
    if (!uploadState) {
      throw new Error('Upload session not found');
    }

    console.log(`[ChunkedUpload] Resuming upload: ${uploadState.file.name}`);
    
    try {
      // Get current status from server
      const statusResponse = await this.getUploadStatus(uploadState.serverUploadId);
      const serverProgress = statusResponse.data;
      
      // Update local state based on server state
      uploadState.uploadedChunks.clear();
      for (let i = 0; i < serverProgress.uploadedChunks; i++) {
        uploadState.uploadedChunks.add(i);
      }
      
      // Continue uploading remaining chunks
      const result = await this.uploadChunks(uploadState);
      
      // Complete upload
      const finalResult = await this.completeUpload(uploadState.serverUploadId);
      
      // Cleanup
      this.activeUploads.delete(uploadId);
      
      console.log(`[ChunkedUpload] Resume completed: ${uploadState.file.name}`);
      return finalResult;
      
    } catch (error) {
      console.error('[ChunkedUpload] Resume failed:', error);
      throw error;
    }
  }

  /**
   * Cancel an active upload
   * @param {string} uploadId - Upload ID to cancel
   */
  async cancelUpload(uploadId) {
    const uploadState = this.activeUploads.get(uploadId);
    if (uploadState) {
      // Mark as cancelled locally first
      uploadState.cancelled = true;
      this.activeUploads.delete(uploadId);

      console.log(`[ChunkedUpload] Cancelling upload: ${uploadState.file.name}`);

      // Call backend to clean up chunks
      try {
        const response = await api.delete(`/content/upload/cancel/${uploadState.serverUploadId}`);
        if (response.data.success) {
          console.log(`[ChunkedUpload] Upload cancelled and ${response.data.data.cleanedChunks} chunks cleaned up`);
        }
      } catch (error) {
        console.error('[ChunkedUpload] Error cancelling upload on server:', error);
        // Don't throw error - local cancellation already happened
      }
    }
  }

  /**
   * Create file chunks
   * @param {File} file - File to chunk
   * @param {number} totalChunks - Total number of chunks
   * @param {number} chunkSize - Size of each chunk
   * @returns {Array} Array of chunk objects
   */
  createChunks(file, totalChunks, chunkSize) {
    const chunks = [];
    for (let i = 0; i < totalChunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);

      chunks.push({
        index: i,
        data: chunk,
        size: chunk.size,
        start,
        end
      });
    }
    return chunks;
  }

  /**
   * Initialize upload session on server
   */
  async initializeUpload(file, totalChunks) {
    const response = await api.post('/content/upload/init', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      totalChunks
    });
    return response.data;
  }

  /**
   * Upload all chunks with retry logic
   */
  async uploadChunks(uploadState) {
    const { chunks, uploadedChunks } = uploadState;

    // Create upload promises for remaining chunks
    const uploadPromises = chunks
      .filter(chunk => !uploadedChunks.has(chunk.index))
      .map(chunk => this.uploadChunkWithRetry(uploadState, chunk));

    // Upload chunks sequentially to avoid overwhelming the connection
    // This prevents timeouts caused by too many concurrent uploads
    const results = [];

    for (const uploadPromise of uploadPromises) {
      try {
        const result = await uploadPromise;
        results.push(result);

        // Update progress after each chunk
        this.updateProgress(uploadState);

        // Check if upload was cancelled
        if (uploadState.cancelled) {
          throw new Error('Upload cancelled by user');
        }

        // Small delay between chunks to prevent overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        // If a chunk fails, we'll let the retry logic handle it
        console.error(`[ChunkedUpload] Chunk upload failed, will be retried:`, error);
        throw error;
      }
    }

    return results;
  }

  /**
   * Upload a single chunk with retry logic
   */
  async uploadChunkWithRetry(uploadState, chunk) {
    const { serverUploadId, failedChunks } = uploadState;
    let retryCount = failedChunks.get(chunk.index) || 0;
    const maxRetries = 3; // Use constant instead of undefined MAX_RETRIES

    while (retryCount < maxRetries) {
      try {
        // Validate uploadId before creating FormData
        if (!serverUploadId) {
          throw new Error(`Missing serverUploadId for chunk ${chunk.index}`);
        }

        const formData = new FormData();
        formData.append('chunk', chunk.data);
        formData.append('uploadId', serverUploadId);
        formData.append('chunkIndex', chunk.index.toString());

        // Debug FormData contents
        console.log(`[ChunkedUpload] Uploading chunk ${chunk.index} with uploadId: ${serverUploadId}`);

        // Verify FormData contains the expected values
        let formDataDebug = {};
        for (let [key, value] of formData.entries()) {
          formDataDebug[key] = key === 'chunk' ? 'File data...' : value;
        }
        console.log('[ChunkedUpload] FormData contents:', formDataDebug);

        const response = await api.post('/content/upload/chunk', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: 600000, // 10 minutes per chunk (increased for S3 uploads)
        });

        // Mark chunk as uploaded
        uploadState.uploadedChunks.add(chunk.index);
        uploadState.uploadedBytes += chunk.size;
        failedChunks.delete(chunk.index);

        // Handle first-chunk preview for video files
        if (chunk.index === 0 && response.data.data?.previewUrl) {
          console.log(`[ChunkedUpload] First-chunk preview generated: ${response.data.data.previewUrl}`);

          // Store preview URL in upload state for immediate use
          uploadState.firstChunkPreviewUrl = response.data.data.previewUrl;

          // Notify progress callback about preview availability
          if (uploadState.onProgress) {
            uploadState.onProgress({
              uploadId: uploadState.uploadId,
              progress: Math.round((uploadState.uploadedBytes / uploadState.file.size) * 100),
              uploadedBytes: uploadState.uploadedBytes,
              totalBytes: uploadState.file.size,
              uploadedChunks: uploadState.uploadedChunks.size,
              totalChunks: uploadState.totalChunks,
              previewUrl: response.data.data.previewUrl,
              isFirstChunkPreview: true,
              speed: this.calculateUploadSpeed(uploadState)
            });
          }
        }

        console.log(`[ChunkedUpload] Chunk ${chunk.index + 1}/${uploadState.totalChunks} uploaded`);
        return response.data;

      } catch (error) {
        retryCount++;
        failedChunks.set(chunk.index, retryCount);

        // Get detailed error information
        let errorMessage = error.message;
        if (error.response?.data?.message) {
          errorMessage = error.response.data.message;
        }

        console.error(`[ChunkedUpload] Chunk ${chunk.index} failed (attempt ${retryCount}/${maxRetries}):`, {
          error: errorMessage,
          status: error.response?.status,
          data: error.response?.data,
          chunkIndex: chunk.index,
          uploadId: serverUploadId
        });

        if (retryCount >= maxRetries) {
          throw new Error(`Chunk ${chunk.index} failed after ${maxRetries} attempts: ${errorMessage}`);
        }

        // Exponential backoff delay
        const retryDelayBase = 1000; // 1 second base delay
        const delay = retryDelayBase * Math.pow(2, retryCount - 1);
        await new Promise(resolve => setTimeout(resolve, delay));

        // Notify about retry
        if (uploadState.onRetry) {
          uploadState.onRetry(chunk.index, retryCount, maxRetries);
        }
      }
    }
  }

  /**
   * Complete the upload on server
   */
  async completeUpload(serverUploadId) {
    const response = await api.post('/content/upload/complete', {
      uploadId: serverUploadId
    });
    return response.data;
  }

  /**
   * Create content with both fileUrl and previewUrl from chunked upload
   * This saves both URLs to the database at the same time
   */
  async createContentWithUpload(serverUploadId, contentData) {
    const response = await api.post('/content/create-with-upload', {
      uploadId: serverUploadId,
      ...contentData
    });
    return response.data;
  }

  /**
   * Calculate upload speed based on upload state
   */
  calculateUploadSpeed(uploadState) {
    if (!uploadState.startTime) {
      return 0;
    }

    const elapsedTime = (Date.now() - uploadState.startTime) / 1000; // seconds
    if (elapsedTime === 0) {
      return 0;
    }

    const uploadedBytes = uploadState.uploadedBytes || 0;
    const speedBytesPerSecond = uploadedBytes / elapsedTime;

    // Return speed in MB/s
    return Math.round((speedBytesPerSecond / (1024 * 1024)) * 100) / 100;
  }

  /**
   * Calculate upload speed based on upload state
   */
  calculateUploadSpeed(uploadState) {
    if (!uploadState.startTime) {
      return 0;
    }

    const elapsedTime = (Date.now() - uploadState.startTime) / 1000; // seconds
    if (elapsedTime === 0) {
      return 0;
    }

    const uploadedBytes = uploadState.uploadedBytes || 0;
    const speedBytesPerSecond = uploadedBytes / elapsedTime;

    // Return speed in MB/s
    return Math.round((speedBytesPerSecond / (1024 * 1024)) * 100) / 100;
  }

  /**
   * Get upload status from server
   */
  async getUploadStatus(serverUploadId) {
    const response = await api.get(`/content/upload/status/${serverUploadId}`);
    return response.data;
  }

  /**
   * Update progress and calculate upload speed/ETA
   */
  updateProgress(uploadState) {
    const { uploadedChunks, totalChunks, uploadedBytes, file, startTime, onProgress } = uploadState;
    
    const progress = Math.round((uploadedChunks.size / totalChunks) * 100);
    const currentTime = Date.now();
    const elapsedTime = currentTime - startTime;
    
    // Calculate upload speed (bytes per second)
    const speed = uploadedBytes / (elapsedTime / 1000);
    
    // Calculate ETA (estimated time remaining)
    const remainingBytes = file.size - uploadedBytes;
    const eta = speed > 0 ? remainingBytes / speed : 0;
    
    if (onProgress) {
      onProgress({
        progress,
        uploadedChunks: uploadedChunks.size,
        totalChunks,
        uploadedBytes,
        totalBytes: file.size,
        speed,
        eta,
        elapsedTime
      });
    }
  }

  /**
   * Format bytes to human readable string
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Format time to human readable string
   */
  formatTime(seconds) {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m ${Math.round(seconds % 60)}s`;
    return `${Math.round(seconds / 3600)}h ${Math.round((seconds % 3600) / 60)}m`;
  }
}

// Export singleton instance
export default new ChunkedUploadService();
